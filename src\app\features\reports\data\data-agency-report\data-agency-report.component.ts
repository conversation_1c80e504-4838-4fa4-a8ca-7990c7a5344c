import {
  Component,
  EventEmitter,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, combineLatest, skipWhile, switchMap, take, takeUntil } from 'rxjs';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading } from 'src/app/reducers/global-settings/global-settings.reducer';

import {
  PAGE_SIZE,
  REPORT_FILTERS_KEY_LABEL,
  SHOW_ENTRIES,
  USER_VISIBILITY,
} from 'src/app/app.constants';
import { DataDateType, IntegrationSource, LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  getTotalCountForReports,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { FetchAgencyNameList } from 'src/app/reducers/lead/lead.actions';
import {
  getAgencyNameList,
  getAgencyNameListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';

import * as moment from 'moment';
import { AnimationOptions } from 'ngx-lottie';
import { ReportsFilter } from 'src/app/core/interfaces/reports.interface';
import {
  FetchDataAgencyExportSuccess,
  FetchDataReportsAgency,
  UpdateDataAgencyFilterPayload,
} from 'src/app/reducers/data-reports/data-reports.action';
import {
  getDataAgencyFiltersPayload,
  getDataReportsAgenciesList,
} from 'src/app/reducers/data-reports/data-reports.reducers';
import {
  FetchDataCities,
  FetchDataCountries,
  FetchDataSourceList,
  FetchDataStates,
  FetchDataStatus,
  FetchDataSubSourceList,
  UpdateDataFilterPayload,
} from 'src/app/reducers/data/data-management.actions';
import {
  getDataCities,
  getDataCitiesIsLoading,
  getDataCountries,
  getDataCountriesIsLoading,
  getDataSourceList,
  getDataSourceListIsLoading,
  getDataStates,
  getDataStatesIsLoading,
  getDataStatusList,
  getDataSubSourceList,
  getDataSubSourceListIsLoading,
} from 'src/app/reducers/data/data-management.reducer';
import {
  getPermissions,
  getPermissionsIsLoading,
} from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchOnlyReporteesWithInactive,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getOnlyReporteesWithInactive,
  getOnlyReporteesWithInactiveIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { environment } from 'src/environments/environment';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'data-agency-report',
  templateUrl: './data-agency-report.component.html',
})
export class DataAgencyReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  columnDropDown: { field: string; hide: boolean }[] = [];
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  currentView: 'table' | 'graph' = 'table';
  agencyList: any;
  rowData: Array<any> = [];
  filtersPayload: ReportsFilter;
  appliedFilter: any;
  canExportAllUsers: boolean = false;
  canViewAllUsers: boolean = false;
  canViewReportees: boolean = false;
  canExportReportees: boolean = false;
  getPages = getPages;
  dateTypeList: Array<any> = Object.values(DataDateType).slice(0, 5);
  visibilityList: Array<Object> = USER_VISIBILITY.slice(0, 2);
  subSourceList: any;
  isSubSourceListLoading: boolean = true;
  allUsers: Array<any> = [];
  onlyReportees: Array<any> = [];
  users: Array<any> = [];
  reportees: Array<any> = [];
  showLeftNav: boolean = true;
  sourceList: any[] = [];
  isSourceListLoading: boolean = true;
  isSourcesLoading: boolean = false;
  dataAgenciesTotalCount: any;
  statusList: any = [];
  statusIdMap: any = {};
  sourceIdMap: any = {};
  isGetFromTextCalled: boolean = false;
  moment = moment;
  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;
  showFilters: boolean = false;
  isReportLoading: boolean = true;
  isPermissionsLoading: boolean = true;
  isUsersListForReassignmentLoading: boolean = true;
  isOnlyReporteesWithInactiveLoading: boolean = true;
  cities: string[];
  citiesIsLoading: boolean = true;
  states: string[];
  statesIsLoading: boolean = true;
  countryIsLoading: boolean = true;
  countryList: any[];

  isAgencyNameListLoading: boolean;
  userData: any;
  currentDate: Date = new Date();
  toDate: any = new Date();
  fromDate: any = new Date();
  onPickerOpened = onPickerOpened;
  filteredColumnDefsCache: any[] = [];
  s3BucketUrl: string = environment.s3ImageBucketURL;
  allDataSubSourceList: any;

  @ViewChild('reportsGraph') reportsGraph: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    private router: Router,
    private modalService: BsModalService,
    private shareDataService: ShareDataService,
    private modalRef: BsModalRef
  ) { }

  ngOnInit() {
    this.headerTitle.setTitle('Data - Agency Report');
    this.metaTitle.setTitle('CRM | Reports');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.fetchDataForReports();
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });
    // Set up the source and subsource handling
    const allSources$ = this._store.select(getAllSources);
    const dataSourceList$ = this._store.select(getDataSourceList);

    combineLatest([allSources$, dataSourceList$])
      .pipe(takeUntil(this.stopper))
      .subscribe(([leadSource, dataSource]) => {
        if (leadSource && dataSource) {
          const matched = dataSource
            .filter((data: any) =>
              leadSource.some((lead: any) => lead?.value === data?.value && lead?.isEnabled))
            .map((data: any) => {
              const matchedLead = leadSource.find((lead: any) => lead?.value === data?.value);
              return {
                ...data,
                isEnabled: matchedLead?.isEnabled ?? false,
                isDefault: matchedLead?.isDefault ?? false
              };
            });
          const sorted = matched.sort((a: any, b: any) => {
            if (a.isEnabled === b.isEnabled) {
              return a.displayName.localeCompare(b.displayName);
            }
            return a.isEnabled ? -1 : 1;
          });
          this.sourceList = sorted;
          this.updateSubSource()

          this.sourceIdMap = {};
          sorted.forEach((source: any) => {
            if (source.id && source.displayName) {
              this.sourceIdMap[source.id] = source.displayName;
            }
          });
        }
      });

    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });
  }

  fetchDataForReports() {
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });
    this._store
      .select(getDataAgencyFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = { ...data, isNavigatedFromReports: true };
        this.pageSize = this.filtersPayload?.pageSize;
        const userStatus =
          this.filtersPayload?.userStatus === undefined
            ? 1
            : this.filtersPayload?.userStatus;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          userStatus: userStatus,
          visibility: this.filtersPayload?.userStatus,
          dateType: DataDateType[Number(this.filtersPayload?.dateType)],
          date: [
            patchTimeZoneDate(
              this.filtersPayload?.fromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              this.filtersPayload?.toDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ],
          withTeam: this.filtersPayload?.IsWithTeam,
          users: this.filtersPayload?.UserIds,
          search: this.filtersPayload?.SearchText,
          sources: this.filtersPayload?.SourceIds,
          subSources: this.filtersPayload?.SubSources,
          agencies: this.filtersPayload?.AgencyNames,
          cities: this.filtersPayload?.Cities,
          states: this.filtersPayload?.States,
        };
      });

    this._store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.agencyList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getAgencyNameListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAgencyNameListLoading = isLoading;
      });
    this._store
      .select(getDataSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isSourceListLoading = isLoading;
      });

    this._store
      .select(getPermissionsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isPermissionsLoading = isLoading;
      });

    this._store
      .select(getPermissions)
      .pipe(
        skipWhile(() => this.isPermissionsLoading),
        take(1)
      )
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExportAllUsers = permissionsSet.has(
          'Permissions.Reports.ExportAllUsers'
        );
        this.canExportReportees = permissionsSet.has(
          'Permissions.Reports.ExportReportees'
        );
        this.canViewAllUsers = permissionsSet.has(
          'Permissions.Reports.ViewAllUsers'
        );
        this.canViewReportees = permissionsSet.has(
          'Permissions.Reports.ViewReportees'
        );
        if (this.canViewAllUsers) {
          this._store.dispatch(new FetchUsersListForReassignment());
        } else if (this.canViewReportees) {
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
      });

    this.fetchUsersList();

    this._store
      .select(getDataCities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.cities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getDataCitiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.citiesIsLoading = isLoading;
      });

    this._store
      .select(getDataStates)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.states = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getDataStatesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.statesIsLoading = data;
      });

    this._store
      .select(getDataCountries)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryList = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getDataCountriesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.countryIsLoading = data;
      });

    this._store
      .select(getDataSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allDataSubSourceList = data
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.updateSubSource()
      });
    this._store
      .select(getDataSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isSubSourceListLoading = isLoading;
      });
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
    let rowData: any;
    this._store.dispatch(new FetchDataStatus());

    // Ensure data is fetched after permissions are processed
    this._store.select(getPermissions).pipe(
      skipWhile((permissions: any) => !permissions?.length),
      take(1),
      takeUntil(this.stopper)
    ).subscribe(() => {
      this.filterFunction();
    });
    this._store
      .select(getDataReportsAgenciesList)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          this.isReportLoading = data?.isLoading;
          rowData = data?.items;
          this.dataAgenciesTotalCount = data.totalCount;

          return this._store
            .select(getDataStatusList)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((statuses: any) => {
        this.statusList = statuses;
        this.rowData = getTotalCountForReports(
          rowData,
          statuses?.map((status: any) => status?.id)
        );
        statuses.forEach((status: any) => {
          this.statusIdMap[status.id] = status.displayName;
        });
        this.initializeGridSettings();
        this.initializeGraphData();
      });
  }

  fetchUsersList() {
    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isUsersListForReassignmentLoading = isLoading;
      });
    this._store
      .select(getUsersListForReassignment)
      .pipe(
        skipWhile(
          () =>
            this.isUsersListForReassignmentLoading || this.isPermissionsLoading
        ),
        takeUntil(this.stopper)
      )
      .subscribe((data: any) => {
        const usersData = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.users = usersData;
        this.allUsers = usersData;
        this.allUsers = assignToSort(this.allUsers, '');
        this.currentVisibility(1, false);
      });

    this._store
      .select(getOnlyReporteesWithInactiveIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isOnlyReporteesWithInactiveLoading = isLoading;
      });
    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(
        skipWhile(
          () =>
            this.isOnlyReporteesWithInactiveLoading || this.isPermissionsLoading
        ),
        takeUntil(this.stopper)
      )
      .subscribe((data: any) => {
        const usersData = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.reportees = usersData;
        this.onlyReportees = usersData;
        this.onlyReportees = assignToSort(this.onlyReportees, '');
        this.currentVisibility(1, false);
      });
  }

  initializeGraphData() {
    this.filteredColumnDefsCache = this.gridOptions?.columnDefs?.filter(
      (col: any) => col.field !== 'Agency Name'
    );
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Agency Name',
        field: 'Agency Name',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [params.data?.name],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<div class="py-16 align-center"><p class= "text-truncate-1 break-all">${params.value[0]}
            </p></div>`;
        },
      },
      {
        headerName: 'Converted Data Count',
        field: 'Converted Data Count',
        filter: false,
        valueGetter: (params: any) => {
          return [
            params.data?.convertedDataCount,
            params?.data?.userId,
            params?.data?.agencyTitle,
            params?.data?.name,
          ];
        },
        minWidth: 70,
        cellRenderer: (params: any) => {
          this.prepareManageDataFilterPayload(
            null,
            params?.value?.[3]
          );
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] || '--'}</p>`
            : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey || event?.event?.metaKey;
          const params = { value: event?.value, data: event?.data };
          const filters: any = this.prepareManageDataFilterPayload(
            null,
            params?.value?.[3]
          );
          if (event.data.agencyTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab(filters);
              return;
            }
            this._store.dispatch(new UpdateDataFilterPayload(filters));
            this.router.navigate(['data/manage-data']);
          }
        },
      },
    ];
    this.statusList.forEach((status: any) => {
      let col: any = {
        headerName: status?.displayName,
        field: status?.id,
        filter: false,
        valueGetter: (params: any) => {
          const count =
            params.data?.data
              ?.filter((data: any) => data?.statusId === status?.id)
              .map((data: any) => data?.dataCount)[0] || 0;
          return [
            count,
            params?.data?.name,
            params?.data?.agencyTitle,
            params?.data?.[`${status?.id}DataCount`],
          ];
        },
        minWidth: 70,
        cellRenderer: (params: any) => {
          this.prepareManageDataFilterPayload(
            status?.id,
            params?.value?.[1],
            status?.status == 'qualified' ? true : false
          );
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] || params.value[3] || '--'}</p>`
            : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey || event?.event?.metaKey;
          const params = { value: event?.value, data: event?.data };
          const filters: any = this.prepareManageDataFilterPayload(
            status?.id,
            params?.value?.[1],
            status?.status == 'qualified' ? true : false
          );
          if (event.data.agencyTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab(filters);
              return;
            }
            this._store.dispatch(new UpdateDataFilterPayload(filters));
            this.router.navigate(['data/manage-data']);
          }
        },
      };

      this.gridOptions?.columnDefs?.push(col);
    });
    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  prepareManageDataFilterPayload(
    statusId: string,
    agencyId: string,
    isQualified: boolean = false
  ) {
    let dataFiltersPayload: any = {
      AssignTo: this.appliedFilter?.users || [],
      IsWithTeam: false,
      SourceIds: this.appliedFilter?.sources || [],
      SubSources: this.appliedFilter?.subSource || [],
      Agencies: [agencyId],
      DateType: DataDateType[this.appliedFilter?.dateType],
      FilterType: 0,
      FirstLevelFilter: 0,
      ProspectVisiblity: statusId && !isQualified ? 0 : 3,
      ProspectSearch: null,
      Cities: this.appliedFilter?.cities || [],
      States: this.appliedFilter?.states || [],
    };

    dataFiltersPayload.StatusIds = statusId ? [statusId] : null;

    if (this.filtersPayload.fromDate) {
      dataFiltersPayload.FromDate = setTimeZoneDate(
        this.appliedFilter?.date?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      );
      dataFiltersPayload.ToDate = setTimeZoneDate(
        this.appliedFilter.date?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      );
    } else {
      dataFiltersPayload.FromDate = null;
      dataFiltersPayload.ToDate = null;
    }

    return dataFiltersPayload;
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  getDataInNewTab(filters: any) {
    window?.open(
      `data/manage-data?leadReportGetData=true&filtersPayload=${encodeURIComponent(
        JSON.stringify(filters)
      )}`,
      '_blank'
    );
  }

  getDataFromCell(operation: string, event: any) {
    this.router.navigate(['data/manage-data']);
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.dateType = this.appliedFilter.dateType;
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.status = operation;
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) filters.IsWithTeam = true;
    this.gridOptionsService.payload = filters;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this._store.dispatch(
      new UpdateDataAgencyFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchDataReportsAgency(true)); // Skip total count for pagination
  }

  onResetDateFilter() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateType: null,
      date: '',
    };
    this.filterFunction();
  }

  currentVisibility(visibility: any, isTopLevelFilter: any) {
    this.appliedFilter.userStatus = visibility;
    this.appliedFilter.pageNumber = 1;
    if (isTopLevelFilter) {
      this.appliedFilter.users = null;
    }
    this.filterFunction();

    if (this.canViewAllUsers) {
      switch (visibility) {
        case 1:
          this.allUsers = this.users?.filter((user: any) => user.isActive);
          break;
        case 2:
          this.allUsers = this.users?.filter((user: any) => !user.isActive);
          break;
        case null:
          this.allUsers = this.users;
          break;
      }
      this.allUsers = assignToSort(this.allUsers, '');
    } else {
      switch (visibility) {
        case 1:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => user.isActive
          );
          break;
        case 2:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => !user.isActive
          );
          break;
        case null:
          this.onlyReportees = this.reportees;
          break;
      }
      this.onlyReportees = assignToSort(this.onlyReportees, '');
    }
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(
      new UpdateDataAgencyFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchDataReportsAgency());
    this.currOffset = 0;
  }

  filterFunction() {
    this.appliedFilter.pageNumber = 1;
    if (
      this.appliedFilter?.dateType?.length ||
      this.appliedFilter?.date?.[0]?.length ||
      this.appliedFilter.users?.length ||
      this.appliedFilter.agencies?.length ||
      this.appliedFilter.subSources?.length ||
      this.appliedFilter.sources?.length ||
      this.appliedFilter.cities?.length ||
      this.appliedFilter.Countries?.length ||
      this.appliedFilter.states?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }

    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      userStatus: this.appliedFilter.userStatus,
      dateType: DataDateType[this.appliedFilter.dateType],
      fromDate: setTimeZoneDate(
        this.appliedFilter?.date?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      toDate: setTimeZoneDate(
        this.appliedFilter.date?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      IsWithTeam: this.appliedFilter.withTeam,
      UserIds: this.appliedFilter.users,
      SearchText: this.searchTerm,
      SourceIds: this.appliedFilter?.sources,
      SubSources: this.appliedFilter.subSources,
      AgencyNames: this.appliedFilter.agencies,
      ReportPermission: this.canViewAllUsers ? 0 : 1,
      ExportPermission: this.canExportAllUsers ? 0 : 1,
      Cities: this.appliedFilter?.cities,
      States: this.appliedFilter?.states,
      // // Countries: this.appliedFilter?.Countries,
    };

    this._store.dispatch(
      new UpdateDataAgencyFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchDataReportsAgency());
    this.currOffset = 0;
  }

  reset() {
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.pageSize,
    };
    this.filterFunction();
  }

  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = [
      'subSources',
      'agencies',
      'agencyNames',
      'cities',
      'states',
    ];

    if (
      [
        'pageSize',
        'pageNumber',
        'visibility',
        'withTeam',
        'userStatus',
        'search',
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'date' && values.length === 2) {
      if (key === 'date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDate = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDate = getTimeZoneDate(
          this.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDate = getTimeZoneDate(
          this.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    } else if (allowedKeys.includes(key)) {
      return values;
    }
    return values?.toString()?.split(',');
  }

  applyAdvancedFilter() {
    this.filterFunction();
    this.modalService.hide();
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.fullName}`;
    });
    return userName;
  }

  onRemoveFilter(key: string, value: string) {
    if (['dateType', 'date'].includes(key)) {
      delete this.appliedFilter[key];
      const dependentKey = key === 'date' ? 'dateType' : 'date';
      if (this.appliedFilter[dependentKey]) {
        delete this.appliedFilter[dependentKey];
      }
    } else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter(
        (_: any, index: number) => {
          const matchIndex = this.appliedFilter[key]?.indexOf(value);
          return index !== matchIndex;
        }
      );
    }
    this.filterFunction();
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    this._store.dispatch(new FetchAgencyNameList());
    this._store.dispatch(new FetchDataSubSourceList());
    this._store.dispatch(new FetchDataCities());
    this._store.dispatch(new FetchDataStates());
    this._store.dispatch(new FetchDataSourceList());
    this._store.dispatch(new FetchAllSources());
    // this._store.dispatch(new FetchDataCountries());
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }

  updateSubSource($event?: any) {
    if ($event?.length) {
      this.subSourceList = [];
      $event.forEach((i: any) => {
        if (i.displayName === '99 Acres') {
          const selectedSubSource = this.allDataSubSourceList['NinetyNineAcres'] || [];
          this.subSourceList = [...this.subSourceList, ...selectedSubSource];
        } else {
          const formattedKey = i.displayName?.replace(/\s+/g, '');
          let selectedSubSource = this.allDataSubSourceList[formattedKey];
          if (!Array.isArray(selectedSubSource)) {
            selectedSubSource = this.allDataSubSourceList[i.displayName] || [];
          }
          this.subSourceList = [...this.subSourceList, ...selectedSubSource];
        }
      });
    } else {
      let subSourceList: string[] = this.sourceList?.flatMap((lead: any): string[] => {
        if (lead?.displayName === '99 Acres') {
          return this.allDataSubSourceList['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        let match = this.allDataSubSourceList[formattedKey];
        if (!match) {
          match = this.allDataSubSourceList[lead?.displayName];
        }
        if (!match && formattedKey?.toLowerCase() === '99acres') {
          match = this.allDataSubSourceList['NinetyNineAcres'];
        }
        return Array.isArray(match) ? match : [];
      }) || [];
      this.subSourceList = subSourceList
    }
  }

  onSelectSource(source: any) {
    if (source) {
      this.updateSubSource(source.displayName);
    } else {
      this.updateSubSource(null);
    }
  }

  exportDataAgencyReport() {
    this._store.dispatch(new FetchDataAgencyExportSuccess(''));

    let initialState: any = {
      payload: {
        ...this.filtersPayload,
        path: 'datareport/agency/status',
        timeZoneId:
          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset:
          this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput(_: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  toggleView() {
    this.currentView = this.currentView === 'graph' ? 'table' : 'graph';
  }

  exportGraphAsPDF() {
    if (this.reportsGraph && this.isGraphExportEnabled()) {
      this.reportsGraph.exportGraph();
    }
  }

  isGraphExportEnabled(): boolean {
    return this.currentView === 'graph' &&
      this.reportsGraph?.isChartReady &&
      !this.reportsGraph?.showSelectionMessage;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
