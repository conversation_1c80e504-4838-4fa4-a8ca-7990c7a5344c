<ng-container *ngIf="canView">
    <div class="pt-12 px-30 position-relative">
        <div class="flex-between">
            <div class="pt-12 align-center">
                <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-16" routerLink='/global-config'></div>
                <span class="icon ic-secondary-filter-solid ic-sm ic-black mr-8"></span>
                <h5 class="fw-600">{{ 'SIDEBAR.leads' | translate }} {{ 'GLOBAL.settings' | translate }}</h5>
            </div>
            <!-- <div class="bg-white border-gray br-4 align-center pl-10 py-10 w-250">
                <div class="bg-light-slate position-absolute m-4 p-6 right-30 z-index-2 border-dark-500 br-4">
                    <span class="ic-search ic-x-xs br-4">
                    </span>
                </div>
                <input type="text" placeholder="search" name="search" class="border-0 outline-0">
            </div> -->
        </div>

        <!-- <div class="bg-white pl-20 py-16 mt-30 flex-between br-6">
            <div>
                <h5 class="fw-600 text-coal">{{ 'BULK_LEAD.duplicate' | translate }} {{'SIDEBAR.leads' | translate}}
                </h5>
                <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.notified-when-anyone'| translate }}</h6>
            </div>
            <div class="align-center mr-50 ph-mr-20 ml-20">
                <div class="text-xs mr-8">Off</div>
                <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold">
                <label for="chkToggle" class="switch-label"></label>
            </div>
        </div> -->
        <form [formGroup]="leadSettingsForm" [ngClass]="{'pe-none blinking' : isGlobalSettingsLoading}">
            <div class="bg-white pl-20 py-16 mt-20 flex-between br-6">
                <div>
                    <h5 class="fw-600">{{ 'REPORTS.export' | translate }} {{'SIDEBAR.leads' | translate}}</h5>
                    <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.export-description'| translate }}</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{leadSettingsForm.get('exportLeads').value == true ? 'on' : 'off'}}</div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'exportLeads') : ''"
                        formControlName="exportLeads" id="chkExportLeads" name="exportLeads"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkExportLeads" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
            <!-- development purpose -->
            <!-- <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">Dubai Lead Form</h5>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{leadSettingsForm.get('isCustomLeadFormEnabled').value == true ?
                        'on' : 'off'}}</div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'exportLeads') : ''"
                        formControlName="isCustomLeadFormEnabled" id="chkConfigurableForm"
                        name="isCustomLeadFormEnabled" [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkConfigurableForm" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div> -->
            <!-- <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">Whatsapp Deep Integration</h5>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{leadSettingsForm.get('isWhatsAppDeepIntegration').value == true ?
                        'on' : 'off'}}</div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'exportLeads') : ''"
                        formControlName="isWhatsAppDeepIntegration" id="chkConfigurableForm"
                        name="isWhatsAppDeepIntegration" [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkConfigurableForm" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div> -->
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">{{ 'GLOBAL.lead' | translate }} {{'LEADS.source' | translate}} {{
                        'GLOBAL.update' | translate }} </h5>
                    <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.source-description'| translate }}</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{leadSettingsForm.get('leadsSourceUpdate').value == true ? 'on' : 'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'leadsSourceUpdate') : ''"
                        formControlName="leadsSourceUpdate" id="chkSourceUpdate" name="leadsSourceUpdate"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkSourceUpdate" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
            <!-- <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">{{ 'SETTINGS.international-number' | translate }}
                        {{'SIDEBAR.leads' | translate}}</h5>
                    <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.international-description'| translate }}</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{leadSettingsForm.get('internationalNo').value == true ? 'on' : 'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'internationalNo') : ''"
                        formControlName="internationalNo" id="chkInternationalNo" name="internationalNo"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkInternationalNo" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div> -->
            <!-- <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">Call Detection
                    </h5>
                    <h6 class="text-dark-gray pt-4">you can enable or disable the call detection for your tenant</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{leadSettingsForm.get('callDetection').value == true ? 'on' : 'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'callDetection') : ''"
                        formControlName="callDetection" id="chkCallDetection" name="callDetection"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkCallDetection" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div> -->
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">Dual Lead Ownership
                    </h5>
                    <h6 class="text-dark-gray pt-4">you can enable or disable the dual lead ownership where each lead
                        has 2 owners </h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{leadSettingsForm.get('dualOwnership').value == true ? 'on' : 'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'dualOwnership') : ''"
                        formControlName="dualOwnership" id="chkDualOwnership" name="dualOwnership"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkDualOwnership" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">Enable Past Date Selection
                    </h5>
                    <h6 class="text-dark-gray pt-4">Enable past date selection for scheduling or marking
                        scheduler-enabled lead statuses</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{leadSettingsForm.get('isPastDateSelectionEnabled').value == true ? 'on'
                        : 'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'isPastDateSelectionEnabled') : ''"
                        formControlName="isPastDateSelectionEnabled" id="chkIsPastDateSelectionEnabled"
                        name="isPastDateSelectionEnabled" [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkIsPastDateSelectionEnabled" class="switch-label"
                        [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">Leads Contact Info
                    </h5>
                    <h6 class="text-dark-gray pt-4">you can enable or disable lead contact info display settings for
                        your tenant</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{leadSettingsForm.get('maskLeadContactNo').value == true ? 'on' : 'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'maskLeadContactNo') : ''"
                        formControlName="maskLeadContactNo" id="chkMaskContactNo" name="maskLeadContactNo"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkMaskContactNo" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">Mandatory Location
                    </h5>
                    <h6 class="text-dark-gray pt-4">you can enable or disable lead location for
                        your tenant</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{leadSettingsForm.get('isLocationMandatory').value == true ? 'on' :
                        'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup, 'isLocationMandatory') : ''"
                        formControlName="isLocationMandatory" id="chkisLocationMandatory" name="isLocationMandatory"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkisLocationMandatory" class="switch-label"
                        [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
            <div class="py-16 mt-12 br-6 bg-white">
                <div class="pl-20 flex-between pb-8">
                    <div>
                        <h5 class="fw-600">Mandatory Notes<span
                                *ngIf="leadSettingsForm.get('mandatoryNotes')?.value && false"
                                class="dot dot-sm text-white bg-coal br-50 ml-6 text-sm">?</span></h5>
                        <h6 class="text-dark-gray pt-4">you can enable adding notes mandatory for status, meeting and
                            {{settingData?.shouldRenameSiteVisitColumn ? 'referral' : 'site-visit'}} updates.</h6>
                    </div>
                    <div class="align-center mr-50 ph-mr-20 ml-20">
                        <div class="text-xs mr-8">{{leadSettingsForm.get('mandatoryNotes')?.value ? 'on' : 'off'}}
                        </div>
                        <input type="checkbox" class="toggle-switch toggle-active-sold"
                            (click)="canUpdate ? openConfirmModal(changePopup, 'mandatoryNotes') : ''"
                            formControlName="mandatoryNotes" id="mandatoryNotes" name="mandatoryNotes"
                            [ngClass]="{'pe-none' : !canUpdate}">
                        <label for="mandatoryNotes" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                    </div>
                </div>
                <div *ngIf="leadSettingsForm.get('mandatoryNotes')?.value" class="border-top">
                    <div *ngFor="let option of mandatoryNotesOptions; let last = last" class="mx-20 py-16"
                        [ngClass]="{'border-bottom': !last}">
                        <label class="checkbox-container">
                            <input type="checkbox" (change)="mandatoryNotesOptionSelected(option)"
                                [checked]="option.value">
                            <span class="checkmark"></span>
                            <h5 class="fw-60 text-dark-800">{{option.label}}</h5>
                            <div class="text-dark-gray text-xs">
                                {{option.description}}
                            </div>
                        </label>
                    </div>
                    <div class="flex-end px-20 border-top pt-16" *ngIf="isMandatoryNotesOptionsValueChanged">
                        <div class="text-decoration-underline cursor-pointer mr-10"
                            (click)="resetMandatoryNotesOptions()">{{ 'BUTTONS.cancel' | translate }}</div>
                        <div class="btn-coal" (click)="saveMandatoryNotes()">{{ 'BUTTONS.save' | translate }}</div>
                    </div>
                </div>
            </div>
            <div class="py-16 mt-12 br-6 bg-white">
                <div class="pl-20 flex-between pb-8">
                    <div>
                        <h5 class="fw-600">Mandatory Projects<span
                                *ngIf="leadSettingsForm.get('mandatoryProjects')?.value && false"
                                class="dot dot-sm text-white bg-coal br-50 ml-6 text-sm">?</span></h5>
                        <h6 class="text-dark-gray pt-4">you can enable adding projects mandatory for meeting,
                            {{settingData?.shouldRenameSiteVisitColumn ? 'referral' : 'site-visit'}} and book updates.
                        </h6>
                    </div>
                    <div class="align-center mr-50 ph-mr-20 ml-20">
                        <div class="text-xs mr-8">{{leadSettingsForm.get('mandatoryProjects')?.value ? 'on' : 'off'}}
                        </div>
                        <input type="checkbox" class="toggle-switch toggle-active-sold"
                            (click)="canUpdate ? openConfirmModal(changePopup, 'mandatoryProjects') : ''"
                            formControlName="mandatoryProjects" id="mandatoryProjects" name="mandatoryProjects"
                            [ngClass]="{'pe-none' : !canUpdate}">
                        <label for="mandatoryProjects" class="switch-label"
                            [ngClass]="{'pe-none' : !canUpdate}"></label>
                    </div>
                </div>
                <div *ngIf="leadSettingsForm.get('mandatoryProjects')?.value" class="border-top">
                    <div *ngFor="let option of mandatoryProjectOptions; let last = last" class="mx-20 py-16"
                        [ngClass]="{'border-bottom': !last}">
                        <label class="checkbox-container">
                            <input type="checkbox" (change)="mandatoryProjectOptionSelected(option)"
                                [checked]="option.value">
                            <span class="checkmark"></span>
                            <h5 class="fw-60 text-dark-800">{{option.label}}</h5>
                            <div class="text-dark-gray text-xs">
                                {{option.description}}
                            </div>
                        </label>
                    </div>
                    <div class="flex-end px-20 border-top pt-16" *ngIf="isMandatoryProjectOptionsValueChanged">
                        <div class="text-decoration-underline cursor-pointer mr-10"
                            (click)="resetMandatoryProjectOptions()">{{ 'BUTTONS.cancel' | translate }}</div>
                        <div class="btn-coal" (click)="saveMandatoryProjects()">{{ 'BUTTONS.save' | translate }}
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="py-16 mt-12 br-6 bg-white">
                <div class="pl-20 flex-between pb-8">
                    <div>
                        <h5 class="fw-600">Mandatory Property<span
                                *ngIf="leadSettingsForm.get('mandatoryProperty')?.value && false"
                                class="dot dot-sm text-white bg-coal br-50 ml-6 text-sm">?</span></h5>
                        <h6 class="text-dark-gray pt-4">you can enable adding property mandatory for meeting and
                            site-visit updates.</h6>
                    </div>
                    <div class="align-center mr-50 ph-mr-20 ml-20">
                        <div class="text-xs mr-8">{{leadSettingsForm.get('mandatoryProperty')?.value ? 'on' : 'off'}}
                        </div>
                        <input type="checkbox" class="toggle-switch toggle-active-sold"
                            (click)="canUpdate ? openConfirmModal(changePopup, 'mandatoryProperty') : ''"
                            formControlName="mandatoryProperty" id="mandatoryProperty" name="mandatoryProperty"
                            [ngClass]="{'pe-none' : !canUpdate}">
                        <label for="mandatoryProperty" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                    </div>
                </div>
                <div *ngIf="leadSettingsForm.get('mandatoryProperty')?.value" class="border-top">
                    <div *ngFor="let option of mandatoryPropertyOptions; let last = last" class="mx-20 py-16"
                        [ngClass]="{'border-bottom': !last}">
                        <label class="checkbox-container">
                            <input type="checkbox" (change)="mandatoryPropertyOptionSelected(option)"
                                [checked]="option.value">
                            <span class="checkmark"></span>
                            <h5 class="fw-60 text-dark-800">{{option.label}}</h5>
                            <div class="text-dark-gray text-xs">
                                {{option.description}}
                            </div>
                        </label>
                    </div>
                    <div class="flex-end px-20 border-top pt-16" *ngIf="isMandatoryPropertyOptionsValueChanged">
                        <div class="text-decoration-underline cursor-pointer mr-10"
                            (click)="resetMandatoryPropertyOptions()">{{ 'BUTTONS.cancel' | translate }}</div>
                        <div class="btn-green" (click)="saveMandatoryProperty()">{{ 'BUTTONS.save' | translate }}</div>
                    </div>
                </div>
            </div> -->
            <div class="py-16 my-12 br-6 bg-white">
                <div class="pl-20 flex-between pb-8">
                    <div>
                        <h5 class="fw-600">{{ 'SETTINGS.allow-duplicates' | translate }} <span
                                *ngIf="leadSettingsForm.get('allowDuplicateLeads')?.value && false"
                                class="dot dot-sm text-white bg-coal br-50 ml-6 text-sm">?</span></h5>
                        <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.allow-duplicates-description'| translate }}</h6>
                    </div>
                    <div class="align-center mr-50 ph-mr-20 ml-20">
                        <div class="px-16" *ngIf="leadSettingsForm.get('allowDuplicateLeads').value">
                            <label class="checkbox-container fw-60 text-dark-800" for="stickyAgentCheckbox">Sticky Agent
                                <input (click)="canUpdate ? openConfirmModal(changePopup, 'stickyAgent') : ''"
                                    type="checkbox" id="stickyAgentCheckbox" formControlName="stickyAgent"
                                    name="stickyAgent">
                                <span class="checkmark"></span>
                            </label>
                        </div>
                        <div class="text-xs mr-8">{{leadSettingsForm.get('allowDuplicateLeads')?.value == true ? 'on' :
                            'off'}}</div>
                        <input type="checkbox" class="toggle-switch toggle-active-sold w-0"
                            (click)="canUpdate ? openConfirmModal(changePopup, 'allowDuplicateLeads') : ''"
                            formControlName="allowDuplicateLeads" id="allowDuplicateLeads" name="allowDuplicateLeads"
                            [ngClass]="{'pe-none' : !canUpdate}">
                        <label for="allowDuplicateLeads" class="switch-label"
                            [ngClass]="{'pe-none' : !canUpdate}"></label>
                    </div>
                </div>
                <div *ngIf="leadSettingsForm.get('allowDuplicateLeads')?.value" class="border-top">
                    <div *ngFor="let option of allowDuplicateOptions; let last = last" class="mx-20 py-16"
                        [ngClass]="{'border-bottom': !last}">
                        <label class="checkbox-container">
                            <input type="checkbox" (change)="allowDuplicateOptionSelected(option)"
                                [checked]="option.value">
                            <span class="checkmark"></span>
                            <h5 class="fw-60 text-dark-800">{{option.label}}</h5>
                            <div class="text-dark-gray text-xs">
                                {{option.description}}
                            </div>
                        </label>
                    </div>
                    <div class="flex-end px-20 border-top pt-16" *ngIf="isAllowDuplicateOptionsValueChanged">
                        <div class="text-decoration-underline cursor-pointer mr-10"
                            (click)="resetAllowDuplicateOptions()">{{ 'BUTTONS.cancel' | translate }}</div>
                        <div class="btn-coal" (click)="saveAllowDuplicates()">{{ 'BUTTONS.save' | translate }}</div>
                    </div>
                </div>
            </div>
            <div class="bg-white mb-12 br-6">
                <div class="bg-white pl-20 py-16 mt-12 flex-between br-6"
                    [ngClass]="leadSettingsForm.get('isLeadRetentionEnabled').value == true ? 'border-bottom': ''">
                    <div>
                        <h5 class="fw-600 align-center">
                            Retention
                        </h5>
                        <h6 class="text-dark-gray mt-4">create lead auto rotation within the team based on shift timing
                        </h6>
                    </div>
                    <div class="align-center mr-50 ph-mr-20 ml-20">
                        <div class="text-xs mr-8">{{leadSettingsForm.get('isLeadRetentionEnabled').value == true ? 'on'
                            :
                            'off'}}
                        </div>
                        <input type="checkbox" class="toggle-switch toggle-active-sold"
                            (click)="canUpdate ? openConfirmModal(changePopup, 'isLeadRetentionEnabled') : ''"
                            formControlName="isLeadRetentionEnabled" id="chkDualLeadRetention"
                            name="isLeadRotationEnabled" [ngClass]="{'pe-none' : !canUpdate}">
                        <label for="chkDualLeadRetention" class="switch-label"
                            [ngClass]="{'pe-none' : !canUpdate}"></label>
                    </div>
                </div>

                <ng-container *ngIf="leadSettingsForm.get('isLeadRetentionEnabled').value">
                    <div class="mx-30 mt-24 pb-24" [ngClass]="{'pe-none blinking' : retentionListIsLoading}">
                        <div class="flex-between mb-8">
                            <h5 class="fw-600">Retention configurations</h5>
                            <button class="btn-coal" id="btnAddNew" data-automate-id="btnAddNew"
                                (click)="AddRetentionHandler()">
                                <span class="ic-add icon ic-xxs mr-8"></span>
                                <span class="ml-4 text-white ip-d-none">Add</span>
                            </button>
                        </div>
                        <div *ngIf="canAddRetention">
                            <lead-rotation-add-group [users]="filteredUsers" [allUsers]="users" [context]="'retention'"
                                [retentionList]="retentionList" (closeAddGroupWithId)="closeAddRetention($event)">
                            </lead-rotation-add-group>
                        </div>
                        <ng-container *ngFor="let group of retentionList">
                            <div class="my-12 br-4" [ngClass]="{'border': !group.isEditEnabled}">
                                <div class="" [ngClass]="{'px-16 py-10': !group.isEditEnabled}">
                                    <div class="flex-between ip-flex-between-unset ip-flex-col">
                                        <div *ngIf="group.isEditEnabled">
                                            <lead-rotation-add-group [users]="filteredUsers" [allUsers]="users"
                                                [group]="group" (closeAddGroupWithId)="closeAddGroupWithId($event)"
                                                [context]="'retention'" [retentionList]="retentionList">
                                            </lead-rotation-add-group>
                                        </div>
                                        <div *ngIf="!group.isEditEnabled" class="w-100">
                                            <div class="flex-between">
                                                <div class="d-flex">
                                                    <h5 class="fw-600 text-black-100 ml-4">{{getTeamName(group?.id)}}
                                                    </h5>
                                                </div>
                                                <div class="d-flex ip-ml-16 ip-mt-4" *ngIf="!group.isEditEnabled">
                                                    <div class="align-center cursor-pointer"
                                                        (click)="editRetentionGroup(group)">
                                                        <div class="icon ic-xxxs ic-light-gray ic-pen-solid"></div>
                                                        <h6 class="fw-semi-bold text-black-100 ml-6">edit</h6>
                                                    </div>
                                                    <div class="ml-10 border h-10 mt-2 mx-4"></div>
                                                    <div class="align-center ml-10 cursor-pointer"
                                                        (click)="deleteLeadRetentionGroup(deletePopup,group)">
                                                        <div class="icon ic-xxxs ic-light-gray ic-trash"></div>
                                                        <h6 class="fw-semi-bold text-black-100 ml-6">delete</h6>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="border-bottom mt-12"></div>
                                            <div class="d-flex flex-wrap ph-flex-col mt-4">
                                                <div class="flex-col ph-ml-0 ml-4 mt-8">
                                                    <div class="text-dark-gray fw-400 text-xs">Lead rotation</div>
                                                    <div class="text-accent-green header-6 fw-600"
                                                        *ngIf="group.isRotationEnabled">
                                                        Enabled
                                                    </div>
                                                    <div class="text-red-350 header-6 fw-600"
                                                        *ngIf="!group.isRotationEnabled">
                                                        Disabled
                                                    </div>
                                                </div>
                                                <div class="ml-24 border h-16 mx-4 mt-16 ip-d-none"></div>
                                                <div class="flex-col ph-ml-0 ml-12 mt-8">
                                                    <div class="text-dark-gray text-sm">Status</div>
                                                    <div class="text-black-200 header-6 fw-600 align-center"
                                                        *ngIf="group?.statuses?.length;else noStatus">
                                                        {{ getStatusInfo(group?.statuses).visible }}
                                                        <ng-container
                                                            *ngIf="getStatusInfo(group?.statuses).remainingCount > 0">
                                                            <span
                                                                class="text-xs text-decoration-underline text-black-200 cursor-pointer ml-4"
                                                                (click)="openStatusPopup(group?.statuses,'Status')">
                                                                + {{ getStatusInfo(group?.statuses).remainingCount }}
                                                                more
                                                            </span>
                                                        </ng-container>
                                                    </div>
                                                </div>
                                                <div class="ml-24 border h-16 mx-4 mt-16 ip-d-none"></div>
                                                <div class="flex-col ph-ml-0 ml-12 mt-8">
                                                    <div class="text-dark-gray text-sm">Sub-Status</div>
                                                    <div class="text-black-200 header-6 fw-600"
                                                        *ngIf="group?.subStatuses?.length; else noStatus">
                                                        {{ getStatusInfo(group?.subStatuses).visible }}
                                                        <ng-container
                                                            *ngIf="getStatusInfo(group?.subStatuses).remainingCount > 0">
                                                            <span
                                                                class="text-xs text-decoration-underline text-black-200 cursor-pointer ml-4"
                                                                (click)="openStatusPopup(group?.subStatuses,'Sub-Status')">
                                                                + {{ getStatusInfo(group?.subStatuses).remainingCount }}
                                                                more
                                                            </span>
                                                        </ng-container>
                                                    </div>
                                                </div>

                                                <!-- <div class="flex-col ml-12 mt-8">
                                                    <div class="text-dark-gray fw-600">Sub-Status :</div>
                                                    <div class="text-black-200 header-6 fw-600">
                                                        {{getStatusName(group?.subStatuses)}}
                                                    </div>
                                                </div> -->
                                                <div class="ml-24 border h-16 mx-4 mt-16 ip-d-none"></div>
                                                <div class="flex-col ph-ml-0 ml-12 mt-8">
                                                    <div class="text-dark-gray text-sm">Shift timing</div>
                                                    <div class="text-black-200 header-6 fw-600">
                                                        {{getTimeZoneTime(group?.configurations?.[0]?.startTime,
                                                        userData?.timeZoneInfo?.baseUTcOffset)}}-
                                                        {{ getTimeZoneTime(group?.configurations?.[0]?.endTime,
                                                        userData?.timeZoneInfo?.baseUTcOffset)}}</div>
                                                </div>
                                                <ng-container *ngIf="group?.isRotationEnabled">
                                                    <div class="ml-24 border h-16 mx-4 mt-16 ip-d-none"></div>
                                                    <div class="flex-col ph-ml-0 ml-12 mt-8">
                                                        <div class="text-dark-gray text-sm">Lead rotation time</div>
                                                        <div class="text-black-200 header-6 fw-600">
                                                            {{convertTimeIntervalToMinutes(group?.configurations?.[0]?.rotationTime)
                                                            || '--'}}
                                                        </div>
                                                    </div>
                                                </ng-container>
                                                <ng-container *ngIf="group?.isRotationEnabled">
                                                    <div class="ml-24 border h-16 mx-4 mt-16 ip-d-none"></div>
                                                    <div class="flex-col ph-ml-0 ml-12 mt-8">
                                                        <div class="text-dark-gray text-sm">Number of rotation</div>
                                                        <div class="text-black-200 header-6 fw-600">
                                                            {{group.configurations?.[0]?.noOfRotation}}
                                                        </div>
                                                    </div>
                                                </ng-container>
                                                <div class="ml-24 border h-16 mx-4 mt-16 ip-d-none"></div>
                                                <div
                                                    class="text-nowrap text-truncate-1 break-all flex-col ph-ml-0 ml-12 mt-8">
                                                    <div class="text-dark-gray text-sm">Days</div>
                                                    <div class="text-black-200 header-6 fw-600">
                                                        {{getDays(group.configurations?.[0]?.dayOfWeeks)}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- <div *ngIf="!group.isEditEnabled">
                                        <div class="d-flex flex-wrap fw-600 text-black-200">
                                            <div class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap"
                                                *ngFor="let groupUser of group.assignmentGroup">
                                                <span class="align-center cursor-pointer"
                                                    [ngClass]="group.isEditEnabled ? 'text-dark-gray' : 'text-coal'">
                                                    {{getAssignedToDetails(groupUser, users, true)}}
                                                </span>
                                                <span *ngIf="group.isEditEnabled"
                                                    class="icon ic-cancel ic-x-xs ml-6 ic-light-gray cursor-pointer">
                                                </span>
                                            </div>
                                        </div>
                                    </div> -->
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </ng-container>

            </div>
            <div class="bg-white mb-12 br-6">
                <div class="bg-white pl-20 py-16 mt-12 flex-between br-6"
                    [ngClass]="leadSettingsForm.get('isLeadRotationEnabled').value == true ? 'border-bottom': ''">
                    <div>
                        <h5 class="fw-600 align-center">
                            Rotation
                            <div class="ml-4 dot dot-sm bg-black-200 cursor-pointer"
                                title="Integration Leads will rotate inside a Created group or a Team within a Configured time.">
                                <span class="m-auto text-white">?</span>
                            </div>
                        </h5>
                        <h6 class="text-dark-gray mt-4">create lead auto rotation within the team based on shift timing
                        </h6>
                    </div>
                    <div class="align-center mr-50 ph-mr-20 ml-20">
                        <div class="text-xs mr-8">{{leadSettingsForm.get('isLeadRotationEnabled').value == true ? 'on' :
                            'off'}}
                        </div>
                        <input type="checkbox" class="toggle-switch toggle-active-sold"
                            (click)="canUpdate ? openConfirmModal(changePopup, 'isLeadRotationEnabled') : ''"
                            formControlName="isLeadRotationEnabled" id="chkDualLeadRotation"
                            name="isLeadRotationEnabled" [ngClass]="{'pe-none' : !canUpdate}">
                        <label for="chkDualLeadRotation" class="switch-label"
                            [ngClass]="{'pe-none' : !canUpdate}"></label>
                    </div>
                </div>

                <ng-container *ngIf="isLeadRotationOpen">
                    <div class="mx-30 mt-24 pb-24" [ngClass]="{'pe-none blinking' : leadRotationIsLoading}">
                        <div class="flex-between mb-8">
                            <h5 class="fw-600">Group</h5>
                            <div class="btn-coal w-100px" (click)="AddGroupHandler()">
                                <span class="ic-add icon ic-xxs mr-8"></span>
                                <span class="text-white text-normal">Add Group</span>
                            </div>
                        </div>
                        <div *ngIf="canAddGroup">
                            <lead-rotation-add-group [leadRotationGroups]="leadRotationGroups" [users]="filteredUsers"
                                [allUsers]="users" [context]="'rotation'"
                                (closeAddGroupWithId)="closeAddGroupWithId($event)">
                            </lead-rotation-add-group>
                        </div>
                        <ng-container *ngFor="let group of leadRotationGroups">
                            <div class="my-12 br-4" [ngClass]="{'border': !group.isEditEnabled}">
                                <div class="" [ngClass]="{'px-16 py-10': !group.isEditEnabled}">
                                    <div class="flex-between  ip-flex-between-unset ip-flex-col">
                                        <div *ngIf="group.isEditEnabled" class="w-100">
                                            <lead-rotation-add-group [leadRotationGroups]="leadRotationGroups"
                                                [users]="filteredUsers" [allUsers]="users" [group]="group"
                                                (closeAddGroupWithId)="closeAddGroupWithId($event)"
                                                [context]="'rotation'">
                                                <!-- <div class="border-bottom mt-16"></div>
                                                <div class="d-flex flex-wrap fw-600 text-black-200">
                                                    <div class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap"
                                                        *ngFor="let groupUser of group.assignmentGroup">
                                                        <span class="align-center cursor-pointer"
                                                            [ngClass]="group.isEditEnabled ? 'text-dark-gray' : 'text-coal'">
                                                            {{getAssignedToDetails(groupUser, allUsers, true)}}
                                                        </span>
                                                        <span *ngIf="group.isEditEnabled"
                                                            class="icon ic-cancel ic-x-xs ml-6 ic-light-gray cursor-pointer">
                                                        </span>
                                                    </div>
                                                </div> -->
                                            </lead-rotation-add-group>
                                        </div>
                                        <div *ngIf="!group.isEditEnabled">
                                            <div class="d-flex">
                                                <h5 class="fw-600 text-black-200">{{"Team Name"}}: </h5>
                                                <p class="fw-semi-bold text-black-200 ml-4">{{group['name']}}</p>
                                            </div>
                                            <div class="d-flex flex-wrap">
                                                <div class="align-center ml-16 mt-8">
                                                    <div class="icon ic-sm ic-light-gray ic-three-person-solid mr-6">
                                                    </div>
                                                    <div class="text-dark-gray text-sm">Team leader :</div>
                                                    <div class="fw-semi-bold text-black-200 ml-4">
                                                        {{getAssignedToDetails(group.manager, users, true)}}
                                                    </div>
                                                </div>
                                                <div *ngIf="group?.configurations?.[0]?.leadSources?.length"
                                                    class="align-center w-20 ml-16 mt-8">
                                                    <div class="icon ic-sm ic-light-gray ic-refine mr-6">
                                                    </div>
                                                    <div class="text-dark-gray text-nowrap text-sm">Source :</div>
                                                    <div [title]="getLeadSourceName(group?.configurations?.[0]?.leadSources)"
                                                        class="fw-semi-bold text-black-200 ml-4 text-truncate-1 break-all">
                                                        {{getLeadSourceName(group?.configurations?.[0]?.leadSources)}}
                                                    </div>
                                                </div>
                                                <div class="align-center ml-16 mt-8">
                                                    <div class="icon ic-sm ic-light-gray ic-secondary-clock mr-6"></div>
                                                    <div class="text-dark-gray text-sm">Shift timing:</div>
                                                    <div class="fw-semi-bold text-black-200 ml-4">
                                                        {{getTimeZoneTime(group?.configurations?.[0]?.startTime,
                                                        userData?.timeZoneInfo?.baseUTcOffset)}}-
                                                        {{ getTimeZoneTime(group?.configurations?.[0]?.endTime,
                                                        userData?.timeZoneInfo?.baseUTcOffset)}}</div>
                                                </div>
                                                <div class="align-center ml-16 mt-8">
                                                    <div class="icon ic-sm ic-light-gray ic-hour-glass mr-6"></div>
                                                    <div class="text-dark-gray text-sm">Lead rotation time:</div>
                                                    <div class="fw-semi-bold text-black-200 ml-4">
                                                        {{convertTimeIntervalToMinutes(group?.configurations?.[0]?.rotationTime)}}
                                                    </div>
                                                </div>
                                                <div class="align-center ml-16 mt-8">
                                                    <div class="icon ic-sm ic-light-gray ic-one-rotate mr-6"></div>
                                                    <div class="text-dark-gray text-sm">Number of rotation:</div>
                                                    <div class="fw-semi-bold text-black-200 ml-4">
                                                        {{group?.configurations?.[0]?.noOfRotation}}
                                                    </div>
                                                </div>
                                                <div *ngIf="timeToMinutes(group?.configurations?.[0]?.bufferTime) !== 0"
                                                    class="align-center ml-16 mt-8">
                                                    <div class="icon ic-sm ic-light-gray ic-hour-glass mr-6"></div>
                                                    <div class="text-dark-gray text-sm">Buffer time:</div>
                                                    <div class="fw-semi-bold text-black-200 ml-4">
                                                        {{convertTimeIntervalToMinutes(group?.configurations?.[0]?.bufferTime)}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="align-center ip-ml-16 ip-mt-4" *ngIf="!group.isEditEnabled">
                                            <div class="align-center cursor-pointer" (click)="editGroup(group)">
                                                <div class="icon ic-xxxs ic-light-gray ic-pen"></div>
                                                <div class="fw-semi-bold text-dark-gray ml-4">edit</div>
                                            </div>
                                            <div class="border h-10 mx-4"></div>
                                            <div class="align-center  cursor-pointer"
                                                (click)="deleteLeadRotationGroup(deletePopup,group)">
                                                <div class="icon ic-xxxs ic-light-gray ic-delete"></div>
                                                <div class="fw-semi-bold text-dark-gray ml-4">delete</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="!group.isEditEnabled">
                                        <div class="border-bottom mt-8"></div>
                                        <div class="d-flex flex-wrap fw-600 text-black-200">
                                            <div class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap"
                                                *ngFor="let groupUser of group.userIds">
                                                <span class="align-center cursor-pointer"
                                                    [ngClass]="group.isEditEnabled ? 'text-dark-gray' : 'text-coal'">
                                                    {{getAssignedToDetails(groupUser, users, true)}}
                                                </span>
                                                <span *ngIf="group.isEditEnabled"
                                                    class="icon ic-cancel ic-x-xs ml-6 ic-light-gray cursor-pointer">
                                                </span>
                                            </div>
                                            <!-- <div class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap"
                                                *ngFor="let childType of status.childTypes">
                                                <span class="align-center cursor-pointer"
                                                    [ngClass]="group.isEditEnabled ? 'text-dark-gray' : 'text-coal'"
                                                    (click)="onAddEditSubStatus('edit', status, childType)">
                                                    {{childType.displayName}} (<span
                                                        *ngIf="!isSubStatusCountLoading else loader">
                                                        {{childType.leadCount || 0}}</span>)
                                                </span>
                                                <span *ngIf="group.isEditEnabled"
                                                    class="icon ic-cancel ic-x-xs ml-6 ic-light-gray cursor-pointer">
                                                </span>
                                            </div> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </ng-container>

            </div>
            <div class="bg-white mb-12 br-6">
                <div class="flex-between py-16 pl-20 pr-50 cursor-pointer" (click)="isOpenSubStatus = !isOpenSubStatus"
                    [ngClass]="{'border-bottom' : isOpenSubStatus}">
                    <div>
                        <h5 class="fw-600">Manage Sub-Status Leads</h5>
                        <h6 class="text-dark-gray mt-4">add, edit and delete lead sub-status</h6>
                    </div>
                    <div class="icon ic-xxs ic-coal"
                        [ngClass]="isOpenSubStatus ? 'ic-triangle-up' : 'ic-triangle-down'">
                    </div>
                </div>
                <ng-container *ngIf="isOpenSubStatus">
                    <div class="m-12 br-4 border" *ngFor="let status of masterLeadStatus">
                        <div class="p-16 position-relative">
                            <span class="fw-600 text-black-200">{{status.actionName}}</span>
                            <div class="border-bottom mt-12"></div>
                            <div class="d-flex flex-wrap fw-600 text-black-200">
                                <div class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap"
                                    *ngFor="let childType of status.childTypes">
                                    <span class="align-center cursor-pointer"
                                        (click)="onAddEditSubStatus('edit', status, childType)">
                                        {{childType.displayName}} (<span *ngIf="!isSubStatusCountLoading else loader">
                                            {{childType?.leadCount || 0}}</span>)
                                    </span>
                                    <span class="icon ic-cancel ic-x-xs ml-6 ic-light-gray cursor-pointer"
                                        (click)="canUpdate ? openConfirmModal(changePopup, 'subStatus',childTypeCount = childType.leadCount,updatingSubStatusId = childType.id) : ''"></span>
                                </div>
                                <div class="btn btn-xs btn-linear-green align-center br-12 mt-12"
                                    (click)="onAddEditSubStatus('add', status)" *ngIf="!status.isFormVisible">
                                    <span class="ic-add icon ic-xs mr-4"></span>Add sub-status
                                </div>
                                <div class="property-adv-filter" *ngIf="status.isFormVisible">
                                    <div class="form-group d-flex ph-flex-wrap mt-12">
                                        <div class="w-50 ph-w-100 mr-20">
                                            <form-errors-wrapper label="sub-status" [control]="subStatusName"
                                                autocomplete="off">
                                                <input type="text" placeholder="ex. Busy" required class="br-20"
                                                    id="subStatusInput" (input)="doesSubStatusExist()"
                                                    [formControl]="subStatusName">
                                            </form-errors-wrapper>
                                        </div>
                                        <div class="d-flex ph-mt-20">
                                            <button class="mr-10 btn-gray"
                                                (click)="status.isFormVisible = false">Cancel</button>
                                            <button class="btn-coal" (click)="subStatusSave()">Save</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pb-4"></div>
                </ng-container>
            </div>
            <div class="bg-white mb-12 br-6">
                <div class="flex-between py-16 pl-20 pr-50 cursor-pointer"
                    (click)="isPriorityListOpen = !isPriorityListOpen"
                    [ngClass]="{'border-bottom' : isPriorityListOpen}">
                    <div>
                        <h5 class="fw-600">{{ 'SETTINGS.priority-list' | translate}}
                            {{'SIDEBAR.leads' | translate}}<span *ngIf="isPriorityListOpen && false"
                                class="dot dot-sm text-white bg-coal br-50 ml-6 text-sm">?</span></h5>
                        <h6 class="text-dark-gray mt-4">{{ 'SETTINGS.priority-list-description'| translate }}</h6>
                    </div>
                    <div class="icon ic-xxs ic-coal"
                        [ngClass]="isPriorityListOpen ? 'ic-triangle-up' : 'ic-triangle-down'"></div>
                </div>
                <ng-container *ngIf="isPriorityListOpen">
                    <div class="mb-20">
                        <div class="px-20 ph-px-16">
                            <div class="my-16 flex-wrap align-center pb-16 border-bottom">
                                <h5 class="fw-600 text-black-200 my-10">{{ "TASK.priority" | translate }}
                                    1</h5>
                                <span class="icon ic-xxs ic-coal ic-arrow-right mr-10 ml-60 ph-ml-10 my-10"></span>
                                <form-errors-wrapper label="{{ 'TASK.priority' | translate }} 1"
                                    [control]="leadSettingsForm.controls.priority1">
                                    <ng-select [virtualScroll]="true" [clearable]="false" [items]="priorityOptions"
                                        ResizableDropdown formControlName="priority1" placeholder="Select option"
                                        (change)="onPriorityOptionChanged()" class="w-150 mr-6">
                                    </ng-select>
                                </form-errors-wrapper>
                                <div class="text-decoration-underline cursor-pointer mr-10 text-red-350 ph-d-none"
                                    (click)="clearPriorityValue(1)">{{ 'BUTTONS.clear' |
                                    translate }}</div>
                                <div class="icon ic-close-secondary ic-sm ic-red cursor-pointer d-none ph-d-block"
                                    (click)="clearPriorityValue(1)"></div>
                            </div>
                            <div class="my-16 flex-wrap align-center pb-16 border-bottom">
                                <h5 class="fw-600 text-black-200 my-10">{{ "TASK.priority" | translate }}
                                    2</h5>
                                <span class="icon ic-xxs ic-coal ic-arrow-right m-10 ml-60 ph-ml-10"></span>
                                <form-errors-wrapper label="{{ 'TASK.priority' | translate }} 2"
                                    [control]="leadSettingsForm.controls.priority2">
                                    <ng-select [virtualScroll]="true" [clearable]="false" [items]="priorityOptions"
                                        ResizableDropdown formControlName="priority2" placeholder="Select option"
                                        (change)="onPriorityOptionChanged()" class="w-150 mr-6">
                                    </ng-select>
                                </form-errors-wrapper>
                                <div class="text-decoration-underline cursor-pointer mr-10 text-red-350 ph-d-none"
                                    (click)="clearPriorityValue(2)">{{ 'BUTTONS.clear' |
                                    translate }}</div>
                                <div class="icon ic-close-secondary ic-sm ic-red cursor-pointer d-none ph-d-block"
                                    (click)="clearPriorityValue(2)"></div>
                            </div>
                            <div class="my-16 flex-wrap align-center pb-16 border-bottom"
                                *ngIf="settingData?.shouldEnablePropertyListing || zoneLocationEnabled">
                                <h5 class="fw-600 text-black-200 my-10">{{ "TASK.priority" | translate }}
                                    3</h5>
                                <span class="icon ic-xxs ic-coal ic-arrow-right m-10 ml-60 ph-ml-10"></span>
                                <form-errors-wrapper label="{{ 'TASK.priority' | translate }} 3"
                                    [control]="leadSettingsForm.controls.priority3">
                                    <ng-select [virtualScroll]="true" [clearable]="false" [items]="priorityOptions"
                                        ResizableDropdown formControlName="priority3" placeholder="Select option"
                                        (change)="onPriorityOptionChanged()" class="w-150 mr-6">
                                    </ng-select>
                                </form-errors-wrapper>
                                <div class="text-decoration-underline cursor-pointer mr-10 text-red-350 ph-d-none"
                                    (click)="clearPriorityValue(3)">{{ 'BUTTONS.clear' |
                                    translate }}</div>
                                <div class="icon ic-close-secondary ic-sm ic-red cursor-pointer d-none ph-d-block"
                                    (click)="clearPriorityValue(3)"></div>
                            </div>
                            <div class="my-16 flex-wrap align-center pb-16 border-bottom" *ngIf="zoneLocationEnabled">
                                <h5 class="fw-600 text-black-200 my-10">{{ "TASK.priority" | translate }}
                                    4</h5>
                                <span class="icon ic-xxs ic-coal ic-arrow-right m-10 ml-60 ph-ml-10"></span>
                                <form-errors-wrapper label="{{ 'TASK.priority' | translate }} 4"
                                    [control]="leadSettingsForm.controls.priority4">
                                    <ng-select [virtualScroll]="true" [clearable]="false" [items]="priorityOptions"
                                        ResizableDropdown formControlName="priority4" placeholder="Select option"
                                        (change)="onPriorityOptionChanged()" class="w-150 mr-6">
                                    </ng-select>
                                </form-errors-wrapper>
                                <div class="text-decoration-underline cursor-pointer mr-10 text-red-350 ph-d-none"
                                    (click)="clearPriorityValue(4)">{{ 'BUTTONS.clear' |
                                    translate }}</div>
                                <div class="icon ic-close-secondary ic-sm ic-red cursor-pointer d-none ph-d-block"
                                    (click)="clearPriorityValue(4)"></div>
                            </div>
                            <div class="my-16 flex-wrap align-center pb-16 border-bottom" *ngIf="zoneLocationEnabled">
                                <h5 class="fw-600 text-black-200 my-10">{{ "TASK.priority" | translate }} 5</h5>
                                <span class="icon ic-xxs ic-coal ic-arrow-right mr-10 ml-60 ph-ml-10 my-10"></span>
                                <form-errors-wrapper label="{{ 'TASK.priority' | translate }} 5"
                                    [control]="leadSettingsForm.controls.priority5">
                                    <ng-select [virtualScroll]="true" [clearable]="false" [items]="priorityOptions"
                                        ResizableDropdown formControlName="priority5" placeholder="Select option"
                                        (change)="onPriorityOptionChanged()" class="w-150 mr-6">
                                    </ng-select>
                                </form-errors-wrapper>
                                <div class="text-decoration-underline cursor-pointer mr-10 text-red-350 ph-d-none"
                                    (click)="clearPriorityValue(5)">{{ 'BUTTONS.clear' | translate }}</div>
                                <div class="icon ic-close-secondary ic-sm ic-red cursor-pointer d-none ph-d-block"
                                    (click)="clearPriorityValue(5)"></div>
                            </div>
                            <div class="my-16 flex-wrap align-center"
                                *ngIf="settingData?.shouldEnablePropertyListing && zoneLocationEnabled">
                                <h5 class="fw-600 text-black-200 my-10">{{ "TASK.priority" | translate }}
                                    6</h5>
                                <span class="icon ic-xxs ic-coal ic-arrow-right m-10 ml-60 ph-ml-10"></span>
                                <form-errors-wrapper label="{{ 'TASK.priority' | translate }} 6"
                                    [control]="leadSettingsForm.controls.priority6">
                                    <ng-select [virtualScroll]="true" [clearable]="false" [items]="priorityOptions"
                                        formControlName="priority6" placeholder="Select option"
                                        (change)="onPriorityOptionChanged()" ResizableDropdown class="w-150 mr-6">
                                    </ng-select>
                                </form-errors-wrapper>
                                <div class="text-decoration-underline cursor-pointer mr-10 text-red-350 ph-d-none"
                                    (click)="clearPriorityValue(6)">{{ 'BUTTONS.clear' | translate }}</div>
                                <div class="icon ic-close-secondary ic-sm ic-red cursor-pointer d-none ph-d-block"
                                    (click)="clearPriorityValue(6)"></div>
                            </div>
                        </div>
                    </div>
                    <div class="py-16 pl-20 pr-20 flex-between border-top">
                        <div class="text-decoration-underline cursor-pointer mr-10 text-red-350"
                            (click)="resetPriorityListValues()">{{ 'BUTTONS.reset-to-default-values' | translate }}
                        </div>
                        <div *ngIf="isPriorityListChanged" class="btn-coal" (click)="savePriorityList()">
                            {{ 'BUTTONS.save' | translate }}</div>
                    </div>
                </ng-container>
            </div>
        </form>
    </div>
    <ng-template #changePopup>
        <div class="p-20">
            <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
            <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div>
            <div class="flex-end mt-30">
                <button class="btn-gray mr-20" (click)="closePopup(false)" *ngIf="settingType != 'subStatus'"
                    id="clkSettingsNo" data-automate-id="clkSettingsNo">
                    {{ 'GLOBAL.no' | translate }}</button>
                <button class="btn-green" *ngIf="settingType != 'subStatus'" (click)="onSave()" id="clkSettingsYes"
                    data-automate-id="clkSettingsYes">
                    {{ 'GLOBAL.yes' | translate }}</button>
                <button class="btn-gray mr-20" (click)="closePopup(false)" *ngIf="settingType == 'subStatus'"
                    id="clkSettingsCancel" data-automate-id="clkSettingsCancel">
                    cancel</button>
                <ng-container *ngIf="settingType == 'subStatus'">
                    <button class="btn-bulk-red"
                        *ngIf="childTypeCount === undefined || childTypeCount === null || childTypeCount <= 0"
                        (click)="deleteSubStatus()" id="clkSettingsDelete" data-automate-id="clkSettingsDelete">Delete
                        {{childTypeCount}}</button>
                </ng-container>
                <ng-container *ngIf="settingType == 'deleteGroup'">
                    <button class="btn-bulk-red"
                        *ngIf="childTypeCount === undefined || childTypeCount === null || childTypeCount <= 0"
                        (click)="deleteLeadRotationGroup()" id="clkSettingsDelete"
                        data-automate-id="clkSettingsDelete">Delete</button>
                </ng-container>
                <ng-container *ngIf="settingType == 'subStatus'">
                    <a class="btn-green" id="clkSettingsRessign" data-automate-id="clkSettingsRessign"
                        *ngIf="childTypeCount>0" (click)="onReassign()"> Reassign</a>
                </ng-container>
            </div>
        </div>
    </ng-template>
    <ng-template #deletePopup>
        <div class="p-20">
            <h3 class="text-black-100 fw-semi-bold mb-20">{{ message }}</h3>
            <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{ notes }}</div>
            <div class="flex-end mt-30">
                <button class="btn-gray mr-20" (click)="modalRef.hide()" id="deleteSettingsCancel"
                    data-automate-id="deleteSettingsCancel">
                    Cancel</button>
                <button class="btn-bulk-red" (click)="deleteGroupConfirm()" id="deleteSettingsDelete"
                    data-automate-id="deleteSettingsDelete">Delete</button>
            </div>
        </div>
    </ng-template>

</ng-container>
<ng-template #loader>
    <div class="container px-4">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling"></div>
        </ng-container>
    </div>
</ng-template>
<ng-template #statusModal>
    <div class="w-100 br-10">
        <div class="flex-between bg-coal px-24 py-12 text-white brtr-10 brtl-10">
            <h4>{{popUpName}}</h4>
            <div class="icon ic-close ic-sm cursor-pointer" (click)="modalService.hide()"></div>
        </div>
        <div class="scrollbar d-flex flex-wrap fw-600 text-dark-gray max-h-300 p-10">
            <div class="bg-secondary px-12 py-8 br-20 mb-12 ml-10 text-nowrap break-all"
                *ngFor="let status of showStatusList">{{status?.displayName}}</div>
        </div>
    </div>
</ng-template>
<ng-template #noStatus>
    <div class="text-black-200 header-6 fw-600">
        --
    </div>
</ng-template>
<ng-template #loading>
    <div class="flex-center h-430">
        <application-loader></application-loader>
    </div>
</ng-template>