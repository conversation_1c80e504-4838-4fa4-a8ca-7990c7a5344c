<div class="justify-center">
    <div class="position-absolute w-80pr bg-slate-140 bottom-12 br-12 box-shadow-10 px-16 py-12 z-index-2 tb-flex-col">
        <div class="flex-between">
            <div class="flex-col">
                <span class="fw-600 text-white text-xl mr-4">{{gridApi?.getSelectedNodes()?.length}}</span>
                <span class="text-white mr-20"> {{ 'GLOBAL.items' | translate}}
                    {{'LEADS.selected' | translate}}</span>
            </div>
            <!-- <div class="text-red-350 fw-semi-bold cursor-pointer" (click)="deselectData()">{{ 'GLOBAL.deselect' |
                    translate }}</div> -->
            <div class="d-flex scrollbar max-w-100-260 tb-max-w-100-190 ip-max-w-100-70 scroll-hide"
                (scroll)="onScrollableContainerScroll()">
                <div class="flex-center">
                    <div class="flex-center">
                        <!-- Visible First 4 Buttons -->
                        <ng-container *ngFor="let button of visibleButtons | slice:0:4">
                            <ng-container
                                *ngTemplateOutlet="buttonTemplate; context: { $implicit: button }"></ng-container>
                        </ng-container>
                        <!-- More Actions Dropdown -->
                        <div #moreActionsDropdownRef="bs-dropdown" dropdown container="body" class="position-relative"
                            *ngIf="visibleButtons?.length > 4">
                            <button class="btn-bulk-black align-center" dropdownToggle id="moreActionsDropdown"
                                data-automate-id="moreActionsDropdown">
                                <span class="ic-swipe-up icon ic-xs mr-8"></span>
                                More Actions
                                <span class="ic-triangle rotate-180 icon ic-xx-xs ml-8"></span>
                            </button>
                            <div class="position-absolute bg-black-80 bottom-45 nleft-55 br-6 py-8 px-20" *dropdownMenu>
                                <ng-container *ngFor="let button of visibleButtons | slice:4">
                                    <ng-container
                                        *ngTemplateOutlet="buttonTemplate; context: { $implicit: button }"></ng-container>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                    <!-- Template for Button -->
                    <ng-template #buttonTemplate let-button>
                        <button *ngIf="button.id === 'bulkConvertToLead'" class="btn-bulk-black  align-center"
                            (click)="openBulkConvertToLeadModal(BulkConvertToLeadModal,confirmationPopup); $event?.stopPropagation()">
                            <span class="ic-user-arrow icon ic-xs mr-8"></span>
                            Convert
                            to
                            Lead
                        </button>
                        <button *ngIf="button.id === 'bulkUpdateStatus'" class="btn-bulk-black align-center"
                            (click)="openBulkUpdateStatusModal(BulkUpdateStatus); $event?.stopPropagation()">
                            <span class="ic-user-arrow icon ic-xs mr-8"></span>
                            Bulk {{ 'GLOBAL.update' | translate }} {{ 'GLOBAL.status' | translate }}
                        </button>
                        <button *ngIf="button.id === 'bulkReassign'" class="btn-bulk-black align-center"
                            (click)="openBulkReassignModal(BulkReassignModal); $event?.stopPropagation()">
                            <span class="ic-user-arrow icon ic-xs mr-8"></span>
                            Bulk {{ 'GLOBAL.reassign' | translate }} {{ 'GLOBAL.leads' | translate }}
                        </button>
                        <button *ngIf="button.id === 'bulkAgency'" class="btn-bulk-black w-100 align-center"
                            (click)="openBulkAgencyModal(BulkAgencyModal); $event?.stopPropagation()">
                            <span class="ic-user-arrow icon ic-xs mr-8"></span>
                            Bulk Agency
                        </button>
                        <button *ngIf="button.id === 'bulkChannelPartner'" class="btn-bulk-black w-100 align-center"
                            id="btnBulkChannelPartner" data-automate-id="btnBulkChannelPartner"
                            (click)="openBulkChannelPartnerModal(BulkChannelPartnerModal)">
                            <span class="ic-handshake-solid icon ic-xs mr-8"></span>
                            Bulk Channel Partner
                        </button>
                        <button *ngIf="button.id === 'bulkCampaign'" class="btn-bulk-black w-100 align-center"
                            id="btnBulkCampaign" data-automate-id="btnBulkCampaign"
                            (click)="openBulkCampaignModal(BulkCampaignModal)">
                            <span class="ic-address-card-solid icon ic-xs mr-8"></span>
                            Bulk Campaign
                        </button>
                        <button *ngIf="button.id === 'bulkWhatsapp'" class="btn-bulk-black w-100 align-center"
                            (click)="openBulkShareModal(); $event?.stopPropagation()">
                            <span class="ic-user-arrow icon ic-xs mr-8"></span>
                            Bulk WhatsApp
                        </button>
                        <button *ngIf="button.id === 'bulkEmail'" class="btn-bulk-black w-100 align-center"
                            (click)="openEmailConfirmation(changePopup,noMail); $event?.stopPropagation()">
                            <span class="ic-user-arrow icon ic-xs mr-8"></span>
                            Bulk Email
                        </button>
                        <button *ngIf="button.id === 'bulkSource'" class="btn-bulk-black w-100 align-center"
                            (click)="openBulkSourceModal(BulkSourceModal); $event?.stopPropagation()">
                            <span class="ic-user-arrow icon ic-xs mr-8"></span>
                            Bulk {{ 'LEADS.source' | translate }}
                        </button>
                        <button *ngIf="button.id === 'bulkRestore'" class="btn-bulk-black w-100 align-center"
                            (click)="bulkPermanentDelete = true; openBulkDeleteModal(BulkDeleteModal); $event?.stopPropagation()">
                            <span class="ic-user-arrow icon ic-xs mr-8"></span>
                            {{ 'LEADS.bulk' | translate }} {{ 'BUTTONS.restore' | translate }}
                        </button>
                        <button *ngIf="button.id === 'bulkDelete'" class="btn-bulk-black w-100 align-center"
                            (click)="bulkPermanentDelete = false; openBulkDeleteModal(BulkDeleteModal); $event?.stopPropagation()">
                            <span class="ic-user-arrow icon ic-xs mr-8"></span>
                            {{ 'LEADS.bulk' | translate }} {{ 'BUTTONS.delete' | translate }}
                        </button>
                    </ng-template>
                </div>
            </div>
        </div>
    </div>
</div>

<ng-template #changePopup>
    <div class="p-20">
        <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
        <!-- <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div> -->
        <div class="flex-end mt-30">
            <div class="btn-gray mr-20" (click)="modalRef.hide()" id="clkSettingsNo" data-automate-id="clkSettingsNo">
                {{ 'GLOBAL.no' | translate }}</div>
            <div class="btn-green" (click)="openBulkEmailPopup()">
                {{ 'GLOBAL.yes' | translate }}</div>
        </div>
    </div>
</ng-template>

<ng-template #noMail>
    <div class="p-20">
        <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
        <!-- <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div> -->
        <div class="flex-end mt-30">
            <button class="btn-green" (click)="modalRef.hide()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
                Close</button>
        </div>
    </div>
</ng-template>

<ng-template #BulkReassignModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{ 'LEADS.bulk' | translate }} {{ 'GLOBAL.reassign' | translate }} </h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"
                *ngIf="!isBulkAssignLoading"></div>
        </div>
        <div class="px-24 scrollbar h-100-108">
            <div class="fw-600 text-coal text-large my-8">Selected Data - {{gridApi?.getSelectedNodes()?.length}}
            </div>
            <div class="scrollbar scroll-hide table-scrollbar ip-w-100-70">
                <table class="table standard-table no-vertical-border">
                    <thead>
                        <tr class="w-100 texxt-nowrap">
                            <th class="w-100px">
                                <span>{{'GLOBAL.name' | translate}}</span>
                            </th>
                            <th class="w-85">
                                <span>{{ 'LEADS.assign-to' | translate }}</span>
                            </th>
                            <th class="w-60">{{ 'GLOBAL.actions' | translate }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-secondary fw-semi-bold max-h-100-240">
                        <ng-container *ngFor="let dataNode of gridApi?.getSelectedNodes()">
                            <tr>
                                <td class="w-100px">
                                    <div class="text-truncate-1 break-all">
                                        {{dataNode?.data?.name}}
                                    </div>
                                </td>
                                <td class="w-85">
                                    <div class="text-truncate-1 break-all">
                                        {{getName(dataNode?.data)}}
                                    </div>
                                </td>
                                <td class="w-60" [ngClass]="{'pe-none blinking': isBulkAssignLoading}">
                                    <a (click)="openConfirmDeleteModal(dataNode?.data?.name, dataNode?.data?.id);trackingService.trackFeature('Web.Data.Button.BulkReassignDelete.Click')"
                                        class="bg-light-red icon-badge">
                                        <span class="icon ic-delete m-auto ic-xxs"></span></a>
                                </td>
                            </tr>
                        </ng-container>
                    </tbody>
                </table>
            </div>
            <form [formGroup]="bulkReassignForm">
                <div class="d-flex flex-wrap">
                    <div *ngFor="let assignmentTypeOption of assignmentTypeOptions"
                        class="form-check form-check-inline p-0 mr-0" [ngClass]="{'pe-none': isUnassignLeadSelected}">
                        <div class="align-center mt-20 mr-20">
                            <input type="radio" [id]="assignmentTypeOption?.value + 'assignmentTypeOptionRadio'"
                                class="radio-check-input border-remove" [value]="assignmentTypeOption.value"
                                formControlName="assignmentType" name="assignmentType"
                                [disabled]="isUnassignLeadSelected"
                                (change)="onAssignmentTypeChange(assignmentTypeOption.label)">
                            <label class="fw-600 text-dark-800 cursor-pointer text-sm ml-8"
                                [for]="assignmentTypeOption?.value + 'assignmentTypeOptionRadio'">
                                {{ assignmentTypeOption.label }}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="field-label-req mt-16">{{ 'LEADS.assign-to' | translate }}</div>
                <form-errors-wrapper [control]="bulkReassignForm.controls['assignedToUsers']"
                    label="{{'LEADS.assign-to' | translate}}" class="ng-select-sm-gray">
                    <ng-select [virtualScroll]="true" placeholder="Select User" name="user" class="bg-white"
                        ResizableDropdown [items]="assignToUsersList" [multiple]="true" [searchable]="true"
                        [closeOnSelect]="false" formControlName="assignedToUsers"
                        (change)="assignToUserListChanged(); trackingService.trackFeature('Web.Data.Dropdown.BulkReassignAssignTo.Click')">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.label}}</div>
                        </ng-template>
                    </ng-select>
                </form-errors-wrapper>
                <h5 class="text-dark-800 fw-600 mt-16">{{'GLOBAL.select-your-preferences' | translate}}</h5>
                <div class="d-flex">
                    <label class="checkbox-container mt-12" [ngClass]="{'pe-none': isUnassignLeadSelected}"
                        *ngIf="canUpdateSource && canViewLeadSource && globalSettingsData?.isLeadSourceEditable">
                        <input type="checkbox" [checked]="bulkReassignForm.get('isChangeSourceSelected').value" (click)="trackingService.trackFeature('Web.Data.Options.changeSource.Click')
                    " [disabled]="isUnassignLeadSelected" formControlName="isChangeSourceSelected">
                        <span class="checkmark"></span> <span class="fw-600 text-sm">{{'GLOBAL.change-source'
                            | translate}}</span>
                    </label>
                </div>
                <div class="d-flex w-100 ng-select-sm"
                    *ngIf="bulkReassignForm.get('isChangeSourceSelected').value && canUpdateSource && canViewLeadSource && globalSettingsData?.isLeadSourceEditable">
                    <div class="w-50">
                        <div class="field-label-req text-sm">{{'LEADS.source' | translate}}</div>
                        <form-errors-wrapper [control]="bulkReassignForm.controls['selectedSource']"
                            label="{{'LEADS.source' | translate}}">
                            <ng-select [virtualScroll]="true" placeholder="Select Source" name="selectedSource"
                                class="bg-white" ResizableDropdown [ngClass]="{'blinking pe-none': isSourcesLoading}"
                                formControlName="selectedSource"
                                (change)="onSourceChange(); trackingService.trackFeature('Web.Data.Options.Source.Click')">
                                <ng-option *ngFor="let source of leadSources" [value]="source">
                                    <div class="dropdown-position">
                                        <span class="text-truncate-1 break-all">{{ source.displayName }}</span>
                                    </div>
                                </ng-option>
                            </ng-select>
                        </form-errors-wrapper>
                    </div>
                    <div class="w-50 ml-10">
                        <div class="field-label text-sm">{{'LEADS.sub-source' | translate}}</div>
                        <ng-select [virtualScroll]="true" placeholder="Select Sub Source" name="selectedSubSource"
                            class="bg-white" ResizableDropdown [items]="filteredSubSourceList" [searchable]="true"
                            formControlName="selectedSubSource">
                        </ng-select>
                    </div>
                </div>
                <div class="d-flex">
                    <label class="checkbox-container mt-16" [ngClass]="{'pe-none': isUnassignLeadSelected}">
                        <input type="checkbox" [checked]="bulkReassignForm.get('isChangeProjectSelected').value"
                            (click)="trackingService.trackFeature('Web.Data.Options.ChangeProject.Click')"
                            [disabled]="isUnassignLeadSelected" formControlName="isChangeProjectSelected">
                        <span class="checkmark"></span> <span class="fw-600 text-sm">{{'GLOBAL.change-project'
                            | translate}}</span>
                    </label>
                </div>
                <div *ngIf="bulkReassignForm.get('isChangeProjectSelected').value" class="w-50">
                    <div class="field-label-req text-sm">{{'GLOBAL.project-name' | translate}}</div>
                    <form-errors-wrapper [control]="bulkReassignForm.controls['selectedProject']"
                        label="{{'GLOBAL.project-name' | translate}}" class="ng-select-sm-gray">
                        <ng-select [virtualScroll]="true" placeholder="Select Project" name="project" class="bg-white"
                            ResizableDropdown [items]="projectList" *ngIf="!projectListIsLoading else fieldLoader"
                            [multiple]="true" [searchable]="true" [closeOnSelect]="false"
                            formControlName="selectedProject">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="project-{{index}}"
                                        data-automate-id="project-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </form-errors-wrapper>
                </div>
            </form>
        </div>
        <div class="flex-center mt-20">
            <button class="btn-gray mr-20" (click)="modalService.hide()" *ngIf="!isBulkAssignLoading">{{
                'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal"
                (click)="updateBulkAssign();trackingService.trackFeature('Web.Data.Button.BulkReassignButton.Click')">
                <span *ngIf="!isBulkAssignLoading else buttonDots">{{ 'BUTTONS.save' | translate }}</span>
            </button>
        </div>
    </div>
</ng-template>
<ng-template #BulkUpdateStatus>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{ 'LEADS.bulk' | translate }} {{ 'GLOBAL.update' | translate }} {{ 'GLOBAL.status' | translate }}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" *ngIf="!isBulkUpdateStatusLoading"
                (click)="modalService.hide()"></div>
        </div>
        <div class="px-24 flex-column">
            <div class="fw-600 text-coal text-large my-8">Selected Data - {{gridApi?.getSelectedNodes()?.length}}
            </div>
            <div class="scrollbar h-100-140 pr-10">
                <div class="scrollbar scroll-hide table-scrollbar ip-w-100-70">
                    <table class="table standard-table no-vertical-border">
                        <thead>
                            <tr class="w-100 text-nowrap">
                                <th class="w-100px">{{'GLOBAL.name' | translate}}</th>
                                <th class="w-85">{{ 'GLOBAL.status' | translate }}</th>
                                <th class="w-60">{{ 'GLOBAL.actions' | translate }}</th>
                            </tr>
                        </thead>
                        <tbody class="text-secondary fw-semi-bold max-h-100-433 min-h-100px">
                            <ng-container *ngFor="let dataNode of gridApi?.getSelectedNodes()">
                                <tr>
                                    <td class="w-100px">
                                        <div class="text-truncate-1 break-all">{{dataNode?.data?.name}}</div>
                                    </td>
                                    <td class="w-85">
                                        <div>
                                            <span class="status-label-badge"
                                                [style]="getBgColor(dataNode?.data?.status?.color)">
                                                <span class="dot dot-xs mr-6 border"
                                                    [style.background-color]="dataNode?.data?.status?.color || '#4B4B4B'"></span>
                                                {{dataNode?.data?.status?.displayName}}
                                            </span>
                                        </div>
                                    </td>
                                    <td class="w-60">
                                        <div class="align-center"
                                            [ngClass]="{ 'pe-none blinking': isBulkUpdateStatusLoading }">
                                            <a (click)="openConfirmDeleteModal(dataNode?.data?.name, dataNode?.data?.id);trackingService.trackFeature('Web.Data.Button.BulkUpdateStatusDelete.Click')"
                                                class="bg-light-red icon-badge">
                                                <span class="icon ic-delete m-auto ic-xxs"></span></a>
                                        </div>
                                    </td>
                                </tr>
                            </ng-container>
                        </tbody>
                    </table>
                </div>
                <div>
                    <form [formGroup]="bulkUpdateStatusForm"
                        [ngClass]="{ 'pe-none blinking': isBulkUpdateStatusLoading }">
                        <div class="field-label-req">Update Status</div>
                        <div class="d-flex flex-wrap">
                            <div *ngFor="let status of statusList" class="oval-radio">
                                <input type="radio" class="btn-check" name="statusId" [id]="status?.id"
                                    autocomplete="off" formControlName="statusId" [value]="status?.id"
                                    (click)="updateStatus(status);trackingService.trackFeature('Web.Data.Button.UpdateStatus.Click')">
                                <label class="btn-outline br-5" [for]="status?.id">{{status?.displayName}}</label>
                            </div>
                        </div>
                        <form-errors-wrapper [control]="bulkUpdateStatusForm.controls['statusId']"
                            label="{{'GLOBAL.status' | translate}}"></form-errors-wrapper>
                        <div *ngIf="selectedStatusList?.length">
                            <div *ngFor="let fields of selectedStatusList"
                                [ngClass]="{'blinking pe-none': isStatusListLoading}">
                                <div *ngIf="fields?.field?.name === 'ScheduledDate'">
                                    <div
                                        [ngClass]="(fields?.isRequired || fields?.validators[0]==='required') ? 'field-label-req' : 'field-label'">
                                        Schedule Date</div>
                                    <form-errors-wrapper [control]="bulkUpdateStatusForm.controls['scheduleDate']"
                                        label="Schedule Date">
                                        <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                                            placeholder="ex. 19/06/2025, 12:00 pm" [min]="minDate"
                                            formControlName="scheduleDate" />
                                        <owl-date-time [hour12Timer]="'true'" #dt1
                                            (afterPickerOpen)="onPickerOpened(currentDate)"
                                            (afterPickerClosed)="onDatePickerClosed('scheduleDate')"
                                            [startAt]="bulkUpdateStatusForm.controls['scheduleDate'].value ? null : currentDate">
                                        </owl-date-time>
                                    </form-errors-wrapper>
                                </div>
                            </div>
                        </div>
                        <div class="field-label">Notes</div>
                        <div class="form-group">
                            <textarea formControlName="notes"
                                (change)="trackingService.trackFeature('Web.Data.DataEntry.BulkUpdateStatusNotes.DataEntry')"
                                class="non-resizable scrollbar" rows="4" placeholder="Add a note .... "></textarea>
                        </div>
                    </form>
                </div>
            </div>
            <div class="flex-center mt-20">
                <button class="btn-gray mr-20" (click)="modalService.hide()" *ngIf="!isBulkUpdateStatusLoading">
                    Cancel
                </button>
                <button class="btn-coal"
                    (click)="bulkUpdateStatus();trackingService.trackFeature('Web.Data.Button.BulkUpdateStatusButton.Click')">
                    <span *ngIf="!isBulkUpdateStatusLoading else buttonDots">Save</span>
                </button>
            </div>
        </div>
    </div>
</ng-template>
<ng-template #BulkAgencyModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{ 'LEADS.bulk' | translate }} {{'REPORTS.agency' | translate}}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
        </div>
        <div class="px-24 h-100-114">
            <div class="fw-600 text-coal text-large my-8">Selected Data -
                {{selectedAgencies?.length}}</div>
            <div class="scrollbar table-scrollbar ph-w-100-60">
                <table class="table standard-table no-vertical-border">
                    <thead>
                        <tr class="w-100">
                            <th class="w-120">Data {{'GLOBAL.name' | translate}}</th>
                            <th class="w-120">{{'REPORTS.agency' | translate}}(s)</th>
                            <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-secondary fw-semi-bold max-h-100-250 scrollbar">
                        <ng-container>
                            <tr *ngFor="let data of selectedAgencies">
                                <td class="w-120"><span class="text-truncate-1 break-all">{{ data.name }}</span></td>
                                <td class="w-120">
                                    <div class="d-flex text-truncate-1 break-all">
                                        <span *ngFor="let agency of data.agencies; let i = index">
                                            <span class="text-nowrap" [title]="getAgencyNames(data)">{{ agency.name
                                                }}</span>
                                            <span *ngIf="i < data.agencies.length - 1">, </span>
                                        </span>
                                    </div>

                                </td>
                                <td class="w-70px">
                                    <a (click)="openConfirmDeleteModal(data?.name, data?.id)"
                                        class="bg-light-red icon-badge" id="clkDeleteBulkProject"
                                        data-automate-id="clkDeleteBulkProject">
                                        <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                                </td>
                            </tr>
                        </ng-container>
                    </tbody>
                </table>
            </div>
            <form [formGroup]="bulkAgencyForm">
                <div class="mt-20">
                    <div class="justify-between">
                        <div class="field-label mt-0">{{'REPORTS.agency' | translate}}(s)</div>
                        <label class="checkbox-container mb-4">
                            <input type="checkbox" formControlName="shouldRemoveExistingAgency">
                            <span class="checkmark"></span>Remove Existing Agency(s)
                        </label>
                    </div>
                </div>
                <form-errors-wrapper [control]="bulkAgencyForm.controls['agency']" label="agency">
                    <ng-select [virtualScroll]="true" [items]="agencyList" *ngIf="!agencyListIsLoading else fieldLoader"
                        ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                        placeholder="{{'GLOBAL.select' | translate}}"
                        (change)="trackingService.trackFeature('Web.Leads.Options.agency.Click')"
                        formControlName="agency" class="bg-white">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                            </div>
                        </ng-template>
                    </ng-select>
                </form-errors-wrapper>
            </form>
        </div>
        <div class="flex-center mt-20 mb-40" *ngIf="!bulkAgencyIsLoading else ratLoader">
            <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal" id="btnSaveBulkAgency" data-automate-id="btnSaveBulkAgency"
                (click)="updateBulkAgency('bulkAgency')">{{ 'BUTTONS.save' | translate }}</button>
        </div>
    </div>
</ng-template>
<ng-template #BulkChannelPartnerModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{ 'LEADS.bulk' | translate }} {{'INTEGRATION.channel-partner' | translate}}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
        </div>
        <div class="px-24 h-100-114">
            <div class="fw-600 text-coal text-large my-8">Selected Data -
                {{selectedChannelPartner?.length}}</div>
            <div class="scrollbar table-scrollbar ph-w-100-60">
                <table class="table standard-table no-vertical-border">
                    <thead>
                        <tr class="w-100">
                            <th class="w-120">Data {{'GLOBAL.name' | translate}}</th>
                            <th class="w-120">{{'INTEGRATION.channel-partner' | translate}}(s)</th>
                            <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-secondary fw-semi-bold max-h-100-250 scrollbar">
                        <ng-container>
                            <tr *ngFor="let data of selectedChannelPartner">
                                <td class="w-120"><span class="text-truncate-1 break-all">{{ data.name }}</span></td>
                                <td class="w-120">
                                    <div class="d-flex text-truncate-1 break-all">
                                        <span *ngFor="let channelPartner of data.channelPartners; let i = index">
                                            <span class="text-nowrap" [title]="getChannelPartnerNames(data)">{{
                                                channelPartner.firmName
                                                }}</span>
                                            <span *ngIf="i < data.channelPartners.length - 1">, </span>
                                        </span>
                                    </div>

                                </td>
                                <td class="w-70px">
                                    <a (click)="openConfirmDeleteModal(data?.name, data?.id)"
                                        class="bg-light-red icon-badge" id="clkDeleteBulkProject"
                                        data-automate-id="clkDeleteBulkProject">
                                        <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                                </td>
                            </tr>
                        </ng-container>
                    </tbody>
                </table>
            </div>
            <form [formGroup]="bulkChannelPartnerForm">
                <div class="mt-20">
                    <div class="justify-between">
                        <div class="field-label mt-0">{{'INTEGRATION.channel-partner' | translate}}(s)</div>
                        <label class="checkbox-container mb-4">
                            <input type="checkbox" formControlName="shouldRemoveExistingChannelPartener">
                            <span class="checkmark"></span>Remove Existing Channel Partner(s)
                        </label>
                    </div>
                </div>
                <form-errors-wrapper [control]="bulkChannelPartnerForm.controls['channelPartner']"
                    label="channelPartner">
                    <ng-select [virtualScroll]="true" [items]="channelPartnerList"
                        *ngIf="!channelPartnerListIsLoading else fieldLoader" ResizableDropdown [multiple]="true"
                        [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                        (change)="trackingService.trackFeature('Web.Leads.Options.agency.Click')"
                        formControlName="channelPartner" class="bg-white">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                            </div>
                        </ng-template>
                    </ng-select>
                </form-errors-wrapper>
            </form>
        </div>
        <div class="flex-center mt-20 mb-40" *ngIf="!bulkCPIsLoading else ratLoader">
            <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal" id="btnSaveBulkChannelPartner" data-automate-id="btnSaveBulkChannelPartner"
                (click)="updateBulkChannelPartner('bulkChannelPartner')">{{ 'BUTTONS.save' | translate }}</button>
        </div>
    </div>
</ng-template>
<ng-template #BulkCampaignModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{ 'LEADS.bulk' | translate }} {{'INTEGRATION.campaign' | translate}}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
        </div>
        <div class="px-24 h-100-114">
            <div class="fw-600 text-coal text-large my-8">Selected Data -
                {{selectedCampaign?.length}}</div>
            <div class="scrollbar table-scrollbar ph-w-100-60">
                <table class="table standard-table no-vertical-border">
                    <thead>
                        <tr class="w-100">
                            <th class="w-120">Data {{'GLOBAL.name' | translate}}</th>
                            <th class="w-120">{{'INTEGRATION.campaign' | translate}}(s)</th>
                            <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-secondary fw-semi-bold max-h-100-250 scrollbar">
                        <ng-container>
                            <tr *ngFor="let data of selectedCampaign">
                                <td class="w-120"><span class="text-truncate-1 break-all">{{ data.name }}</span></td>
                                <td class="w-120">
                                    <div class="d-flex text-truncate-1 break-all">
                                        <span *ngFor="let campaign of data.campaigns; let i = index">
                                            <span class="text-nowrap" [title]="getCampaignNames(data)">{{ campaign.name
                                                }}</span>
                                            <span *ngIf="i < data.campaigns.length - 1">, </span>
                                        </span>
                                    </div>

                                </td>
                                <td class="w-70px">
                                    <a (click)="openConfirmDeleteModal(data?.name, data?.id)"
                                        class="bg-light-red icon-badge" id="clkDeleteBulkProject"
                                        data-automate-id="clkDeleteBulkProject">
                                        <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                                </td>
                            </tr>
                        </ng-container>
                    </tbody>
                </table>
            </div>
            <form [formGroup]="bulkCampaignForm">
                <div class="mt-20">
                    <div class="justify-between">
                        <div class="field-label mt-0">{{'INTEGRATION.campaign' | translate}}(s)</div>
                        <label class="checkbox-container mb-4">
                            <input type="checkbox" formControlName="shouldRemoveExistingCampaign">
                            <span class="checkmark"></span>Remove Existing Campaign(s)
                        </label>
                    </div>
                </div>
                <form-errors-wrapper [control]="bulkCampaignForm.controls['campaign']" label="campaign">
                    <ng-select [virtualScroll]="true" [items]="campaignList"
                        *ngIf="!campaignListIsLoading else fieldLoader" ResizableDropdown [multiple]="true"
                        [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                        (change)="trackingService.trackFeature('Web.Leads.Options.agency.Click')"
                        formControlName="campaign" class="bg-white">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                            </div>
                        </ng-template>
                    </ng-select>
                </form-errors-wrapper>
            </form>
        </div>
        <div class="flex-center mt-20 mb-40" *ngIf="!bulkCampaignIsLoading else ratLoader">
            <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal" id="btnSaveBulkCampaign" data-automate-id="btnSaveBulkCampaign"
                (click)="updateBulkCampaign('bulkCampaign')">{{ 'BUTTONS.save' | translate }}</button>
        </div>
    </div>
</ng-template>
<ng-template #BulkDeleteModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{ 'LEADS.bulk' | translate }} {{ (isDeletedView && !isBulkDataDelete ? 'BUTTONS.restore' :
                'BUTTONS.delete') | translate }}
            </h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer"
                *ngIf="!isBulkDeleteLoading && !isBulkRestoreLoading" (click)="bulkDeleteModalRef.hide()"></div>
        </div>
        <div class="px-12">
            <div class="field-label mb-10">Selected data</div>
            <div class="flex-column table-scrollbar">
                <table class="table standard-table no-vertical-border">
                    <thead>
                        <tr class="w-100">
                            <th>{{'GLOBAL.name' | translate}}</th>
                            <!-- <th>{{ 'GLOBAL.actions' | translate }}</th> -->
                        </tr>
                    </thead>
                    <tbody class="text-secondary fw-semi-bold h-100-189 scrollbar">
                        <ng-container *ngFor="let dataNode of gridApi?.getSelectedNodes()">
                            <tr>
                                <td>
                                    <div class="text-truncate-1 break-all">{{dataNode?.data?.name}}</div>
                                </td>
                                <!-- <td>
                                    <a class="bg-light-red icon-badge"
                                        (click)="openConfirmDeleteModal(dataNode?.data?.name, dataNode?.data?.id)">
                                        <span class="icon ic-delete m-auto ic-xxs"></span></a>
                                </td> -->
                            </tr>
                        </ng-container>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="flex-center">
            <button class="btn-coal mt-20" (click)="bulkDelete()">
                <span *ngIf="(!isBulkDeleteLoading && !isBulkRestoreLoading) else buttonDots">{{ ( isDeletedView &&
                    !isBulkDataDelete ?
                    'BUTTONS.restore' : 'BUTTONS.delete') | translate }}</span>
            </button>
        </div>
    </div>
</ng-template>

<ng-template #BulkConvertToLeadModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>Convert And Assign</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" *ngIf="!isBulkConvertLoading"
                (click)="modalService.hide()"></div>
        </div>
        <div class="px-12">
            <div class="field-label mb-10">Selected data</div>
            <div class="flex-column scrollbar h-100-150 pr-10">
                <div class="table-scrollbar">
                    <table class="table standard-table no-vertical-border">
                        <thead>
                            <tr class="w-100">
                                <th class="w-210">{{'GLOBAL.name' | translate}}</th>
                                <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
                            </tr>
                        </thead>
                        <tbody class="text-secondary fw-semi-bold scrollbar max-h-100-300">
                            <ng-container *ngFor="let dataNode of gridApi?.getSelectedNodes()">
                                <tr>
                                    <td class="w-210">
                                        <div class="text-truncate-1 break-all">{{dataNode?.data?.name}}</div>
                                    </td>
                                    <td class="w-70px" [ngClass]="{ 'pe-none blinking': isBulkConvertLoading }">
                                        <a class="bg-light-red icon-badge"
                                            (click)="openConfirmDeleteModal(dataNode?.data?.name, dataNode?.data?.id)">
                                            <span class="icon ic-delete m-auto ic-xxs"></span></a>
                                    </td>
                                </tr>
                            </ng-container>
                        </tbody>
                    </table>
                </div>
                <form [formGroup]="convertToLeadForm" [ngClass]="{ 'pe-none blinking': isBulkConvertLoading }">
                    <div class="mt-16 mb-4" *ngIf="conditionalStatus.length">
                        <span
                            [ngClass]="convertToLeadForm.controls['status'].value ? 'field-label-req' : 'field-label'">{{
                            'GLOBAL.update' | translate }} {{ 'GLOBAL.status' | translate }}</span>
                        <span class="text-gray text-xs"
                            [ngClass]="convertToLeadForm.controls['status'].value ? 'ml-12' : 'ml-4'"> (If none
                            selected, will be added as new)</span>
                    </div>
                    <div class="d-flex flex-wrap">
                        <ng-container *ngIf="!selectedStatus">
                            <ng-container *ngFor="let status of conditionalStatus; let i = index">
                                <input formControlName="status" type="radio" class="btn-check" [value]="status.id"
                                    id="statusOption{{i}}" data-automate-id="statusOption{{i}}" autocomplete="off"
                                    (change)="statusChanged(status)">
                                <label class="status-badge" for="statusOption{{i}}"
                                    [class.active]="convertToLeadForm.controls['status'].value === status.id">
                                    {{ status.displayName | titlecase }}
                                </label>
                            </ng-container>
                        </ng-container>
                        <div *ngIf="selectedStatus">
                            <div class="align-center mr-4 mb-4 w-100">
                                <div class="m-0 status-badge bg-dark-700 text-white br-5">{{
                                    selectedStatus.displayName | titlecase }}
                                </div>
                                <a class="icon ic-close-secondary ic-light-pale ic-xxs ml-10"
                                    id="clkCancelSelectedBadge" (click)="deselectStatus()"
                                    data-automate-id="clkCancelSelectedBadge"></a>
                            </div>
                            <div class="p-12 position-relative">
                                <ng-container *ngFor="let subStatus of selectedStatus?.childTypes; let i = index">
                                    <input type="radio" class="btn-check" [value]="subStatus.id" id="option{{i}}"
                                        data-automate-id="option{{i}}" autocomplete="off" formControlName="subStatus">
                                    <label class="status-badge"
                                        [class.active]="convertToLeadForm.controls['subStatus'].value === subStatus.id"
                                        for="option{{i}}">
                                        {{ subStatus.displayName }}
                                    </label>
                                </ng-container>
                                <ng-container>
                                    <div class="error-message mb-12"
                                        *ngIf="selectedStatus?.childTypes?.length && !convertToLeadForm.controls['subStatus'].value">
                                        Sub-Status is a required field.
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>

                    <ng-container *ngIf="selectedStatus">
                        <div class="field-label-req">{{'LEAD_FORM.schedule-date' | translate}}</div>
                        <form-errors-wrapper [control]="convertToLeadForm.controls['scheduledDate']"
                            label="{{'LEAD_FORM.schedule-date' | translate}}">
                            <input [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1" [min]="minDate" readonly
                                id="inpAppDateTime" data-automate-id="inpAppDateTime" formControlName="scheduledDate"
                                placeholder="ex. 19/06/2025, 12:00 pm">
                            <owl-date-time #dt1 [hour12Timer]="'true'" (afterPickerOpen)="onPickerOpened(currentDate)"
                                [startAt]="convertToLeadForm.controls['scheduledDate'].value ? null : currentDate"></owl-date-time>
                        </form-errors-wrapper>
                    </ng-container>
                    <div class="field-label-req mt-16">{{ 'LEADS.assign-to' | translate }}</div>
                    <form-errors-wrapper [control]="convertToLeadForm.controls['assignedToUsers']"
                        label="{{'LEADS.assign-to' | translate}}" class="ng-select-sm-gray">
                        <ng-select [virtualScroll]="true" placeholder="Select User" name="user" class="bg-white"
                            nResizableDropdown [items]="assignToUsersList" [multiple]="true" [searchable]="true"
                            [closeOnSelect]="false" formControlName="assignedToUsers">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span
                                        class="text-truncate-1 break-all">{{item.label}}</span></div>
                            </ng-template>
                        </ng-select>
                    </form-errors-wrapper>
                    <div class="field-label">{{'TASK.notes' | translate}}</div>
                    <form-errors-wrapper [control]="convertToLeadForm.controls['notes']"
                        label="{{'TASK.notes' | translate}}">
                        <textarea rows="2" id="txtUpdateStatusNotes" data-automate-id="txtUpdateStatusNotes"
                            formControlName="notes" placeholder="ex. I want to say ..."></textarea>
                    </form-errors-wrapper>
                    <!-- <div class="mt-20 d-flex">
                        <h5 class="fw-600 mr-10">{{'GLOBAL.mark-as' | translate}}</h5>
                        <label class="checkbox-container">
                            <input type="checkbox" [checked]="convertToLeadForm.get('isQualified').value"
                                formControlName="isQualified">
                            <span class="checkmark"></span>
                            <h5>{{'GLOBAL.qualified'
                                | translate}}</h5>
                        </label>
                    </div> -->
                </form>
            </div>
        </div>
        <div class="flex-center mt-20" [ngClass]="{ 'pe-none': isBulkConvertLoading }">
            <button class="btn-gray mr-20" (click)="modalService.hide()" *ngIf="!isBulkConvertLoading">
                Cancel</button>
            <button class="btn-coal"
                (click)="openBulkSuceessModal();trackingService.trackFeature('Web.Data.Button.AssignConvert.Visit')">
                <span *ngIf="!isBulkConvertLoading else buttonDots">Assign And Convert</span>
            </button>
        </div>
    </div>
</ng-template>

<ng-template #buttonDots>
    <div class="container px-4">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling dot-white"></div>
        </ng-container>
    </div>
</ng-template>

<ng-template #confirmationPopup>
    <div class="br-4 pb-20">
        <h2 class="waring-bg-pattern h-100px align-center text-white fw-800 text-xl p-20 brtr-4 brtl-4">Out of
            {{gridApi?.getSelectedNodes().length}}, {{bulkDuplicateData.length}} data have leads associated with their
            contact numbers. Do you want to proceed?</h2>
        <div class="m-10 p-10">
            <div *ngIf="gridApi?.getSelectedNodes().length !== bulkDuplicateData.length">
                <b>Convert to Leads :</b><span class="text-black-200 text-large br-4"> This will create only fresh
                    leads.</span>
            </div>
            <div>
                <b>Proceed :</b><span class="text-black-200 text-large br-4"> This will create duplicate leads.
                </span>
            </div>
        </div>
        <div class="flex-end pr-10 flex-wrap mt-20">
            <div class="btn-gray mr-10" (click)="modalRef.hide()">
                Cancel</div>
            <div class="btn-gray mr-10" *ngIf="gridApi?.getSelectedNodes().length !== bulkDuplicateData.length"
                (click)="proceedWithoutDuplicates()">
                Convert to Leads</div>
            <div class="btn-coal mr-10" (click)="proceedWithDuplicates()">
                Proceed</div>

        </div>
    </div>
</ng-template>
<ng-template #convertPopUp>
    <div class="flex-center-col fw-semi-bold p-16 br-4">
        <div class="h-150">
            <ng-lottie [options]='convertSuccessMessage.success ? successful : sad'></ng-lottie>
        </div>
        <h1 class="text-accent-green">{{convertSuccessMessage?.header}}
        </h1>
        <h3> {{convertSuccessMessage?.message}}</h3>
    </div>
</ng-template>

<ng-template #trackerInfoModal>
    <h5 class="px-20 py-16 fw-semi-bold bg-coal text-white">Bulk Update Status</h5>
    <div class="p-20 flex-center-col">
        <h4 class="text-black-100 fw-600 mb-10 text-center word-break line-break">Bulk Update Status In progress.
        </h4>
        <h5 class="text-black-100 fw-semi-bold text-center word-break line-break">You can check
            <span class="cursor-pointer text-accent-green header-3 fw-600" (click)="openBulkUpdatedStatus()">“Bulk
                Operation
                Tracker”</span> to view updated status
        </h5>
        <button class="btn-green mt-30" (click)="modalService.hide()">
            {{'BULK_LEAD.got-it' | translate}}</button>
    </div>
</ng-template>

<ng-template #BulkSourceModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{ 'LEADS.bulk' | translate }} Source</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" *ngIf="!isBulkSourceLoading"
                (click)="modalService.hide()"></div>
        </div>
        <div class="px-24 flex-column">
            <div class="fw-600 text-coal text-large my-8">Selected Data - {{gridApi?.getSelectedNodes()?.length}}
            </div>
            <div class="scrollbar h-100-140 pr-10">
                <div class="scrollbar scroll-hide table-scrollbar ip-w-100-70">
                    <table class="table standard-table no-vertical-border">
                        <thead>
                            <tr class="w-100 text-nowrap">
                                <th class="w-100px">{{'GLOBAL.name' | translate}}</th>
                                <th class="w-85">Source</th>
                                <th class="w-85">Sub-Source</th>
                                <th class="w-60">{{ 'GLOBAL.actions' | translate }}</th>
                            </tr>
                        </thead>
                        <tbody class="text-secondary fw-semi-bold max-h-100-332">
                            <ng-container *ngFor="let source of selectedSources">
                                <tr>
                                    <td class="w-100px">
                                        <div class="text-truncate-1 break-all">{{source?.name}}</div>
                                    </td>
                                    <td class="w-85">
                                        <div>
                                            <span class="status-label-badge">
                                                {{source?.enquiry?.prospectSource?.displayName}}
                                            </span>
                                        </div>
                                    </td>
                                    <td class="w-85">
                                        <div>
                                            <span class="status-label-badge">
                                                {{source?.enquiry?.subSource}}
                                            </span>
                                        </div>
                                    </td>
                                    <td class="w-60">
                                        <div class="align-center">
                                            <a (click)="openConfirmDeleteModal(source?.name, source?.id);trackingService.trackFeature('Web.Data.Button.BulkSourceDelete.Click')"
                                                class="bg-light-red icon-badge cursor-pointer"
                                                [ngClass]="{ 'pe-none opacity-50': isBulkSourceLoading }">
                                                <span class="icon ic-delete m-auto ic-xxs"></span></a>
                                        </div>
                                    </td>
                                </tr>
                            </ng-container>
                        </tbody>
                    </table>
                </div>
                <div>
                    <form [formGroup]="bulkSourceForm" [ngClass]="{ 'pe-none blinking': isBulkSourceLoading }">
                        <div class="field-label-req">Update Source</div>
                        <form-errors-wrapper [control]="bulkSourceForm.controls['source']" label="source"
                            class="ng-select-sm-gray">
                            <ng-select [virtualScroll]="true" placeholder="Select Source" name="source" class="bg-white"
                                ResizableDropdown formControlName="source"
                                [ngClass]="{'blinking pe-none': isSourcesLoading}"
                                (change)="updateSubSourceForBulkSource(); trackingService.trackFeature('Web.Data.Options.Source.Click')">
                                <ng-option *ngFor="let source of leadSources" [value]="source"
                                    [disabled]="!source.isEnabled" [ngClass]="{'pe-none': !source.isEnabled}">
                                    <div class="dropdown-position">
                                        <span class="text-truncate-1 break-all">{{ source.displayName }}</span>
                                        <span class="text-disabled" *ngIf="!source.isEnabled"> (Disabled)</span>
                                    </div>
                                </ng-option>
                            </ng-select>
                        </form-errors-wrapper>
                        <ng-container *ngIf="bulkSourceForm.get('source')?.value">
                            <div class="field-label">Sub Source</div>
                            <form-errors-wrapper [control]="bulkSourceForm.controls['subsource']" label="sub source"
                                class="ng-select-sm-gray">
                                <ng-select [virtualScroll]="true" placeholder="Select Sub Source" name="subsource"
                                    class="bg-white" ResizableDropdown [items]="filteredSubSourceList"
                                    [searchable]="true" formControlName="subsource">
                                </ng-select>
                            </form-errors-wrapper>
                        </ng-container>
                    </form>
                </div>
            </div>
            <div class="flex-center mt-20">
                <button class="btn-gray mr-20" (click)="modalService.hide()" *ngIf="!isBulkSourceLoading">
                    Cancel
                </button>
                <button class="btn-coal"
                    (click)="updateBulkSource();trackingService.trackFeature('Web.Data.Button.BulkSourceButton.Click')">
                    <span *ngIf="!isBulkSourceLoading else buttonDots">Save</span>
                </button>
            </div>
        </div>
    </div>
</ng-template>
<ng-template #fieldLoader>
    <ng-select [virtualScroll]="true" class="pe-none blinking"></ng-select>
</ng-template>